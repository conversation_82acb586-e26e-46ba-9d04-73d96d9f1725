{"info": {"name": "PortlessApp - Local Development", "description": "Collection for testing PortlessApp Firebase Functions locally", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://127.0.0.1:5001/demo-portless/us-central1", "type": "string"}, {"key": "authToken", "value": "", "type": "string", "description": "Firebase Auth Token - Get from browser after login"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "item": [{"name": "🌐 Public APIs", "item": [{"name": "Public API - Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/portless-public-httpsApi/health", "host": ["{{baseUrl}}"], "path": ["portless-public-httpsApi", "health"]}}}, {"name": "Public API - Root", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/portless-public-httpsApi", "host": ["{{baseUrl}}"], "path": ["portless-public-httpsApi"]}}}]}, {"name": "🛒 Seller Portal APIs", "item": [{"name": "Seller API - Root", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/portless-seller-httpsApi", "host": ["{{baseUrl}}"], "path": ["portless-seller-httpsApi"]}}}, {"name": "Seller API - Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/portless-seller-httpsApi/health", "host": ["{{baseUrl}}"], "path": ["portless-seller-httpsApi", "health"]}}}]}, {"name": "🔧 Admin APIs", "item": [{"name": "Admin API - Root", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/portless-admin-httpsApi", "host": ["{{baseUrl}}"], "path": ["portless-admin-httpsApi"]}}}, {"name": "Admin API - Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/portless-admin-httpsApi/health", "host": ["{{baseUrl}}"], "path": ["portless-admin-httpsApi", "health"]}}}]}, {"name": "📦 OMS APIs", "item": [{"name": "OMS API - Root", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/oms-api", "host": ["{{baseUrl}}"], "path": ["oms-api"]}}}, {"name": "OMS API - Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/oms-api/health", "host": ["{{baseUrl}}"], "path": ["oms-api", "health"]}}}]}, {"name": "🏢 Organization APIs", "item": [{"name": "Organization API - Root", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/services-organization-api", "host": ["{{baseUrl}}"], "path": ["services-organization-api"]}}}]}, {"name": "📦 Mabang APIs", "item": [{"name": "Mabang - Orders Sync", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/mabang-orders-sync-process", "host": ["{{baseUrl}}"], "path": ["mabang-orders-sync-process"]}}}, {"name": "Mabang - Products Sync", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/mabang-products-sync-process", "host": ["{{baseUrl}}"], "path": ["mabang-products-sync-process"]}}}, {"name": "Mabang - Orders Backfill", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/mabang-http-orders-backfill", "host": ["{{baseUrl}}"], "path": ["mabang-http-orders-backfill"]}}}]}, {"name": "📍 Tracking APIs", "item": [{"name": "17Track - Update Tracking", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/track17-updateTracking", "host": ["{{baseUrl}}"], "path": ["track17-updateTracking"]}}}, {"name": "17Track - Save to GCS", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/track17-http-onTrackingUpdateSaveToGcs", "host": ["{{baseUrl}}"], "path": ["track17-http-onTrackingUpdateSaveToGcs"]}}}]}, {"name": "🌍 Zonos APIs", "item": [{"name": "Zonos API - Root", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/services-zonos-api", "host": ["{{baseUrl}}"], "path": ["services-zonos-api"]}}}]}]}