{"id": "portless-local-dev", "name": "PortlessApp - Local Development", "values": [{"key": "baseUrl", "value": "http://127.0.0.1:5001/demo-portless/us-central1", "type": "default", "enabled": true}, {"key": "authToken", "value": "", "type": "secret", "enabled": true}, {"key": "emulatorUI", "value": "http://127.0.0.1:4000", "type": "default", "enabled": true}, {"key": "sellerPortal", "value": "http://localhost:3000", "type": "default", "enabled": true}, {"key": "adminPortal", "value": "http://127.0.0.1:5100", "type": "default", "enabled": true}, {"key": "firestoreEmulator", "value": "http://127.0.0.1:8080", "type": "default", "enabled": true}, {"key": "authEmulator", "value": "http://127.0.0.1:9099", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}