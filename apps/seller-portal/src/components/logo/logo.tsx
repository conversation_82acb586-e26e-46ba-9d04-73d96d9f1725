import type { BoxProps } from '@mui/material/Box';

import { forwardRef } from 'react';

import Box from '@mui/material/Box';
// import { useTheme } from '@mui/material/styles';

import { RouterLink } from '~/routes/components';

import { logoClasses } from './classes';

// ----------------------------------------------------------------------

export type LogoProps = BoxProps & {
  href?: string;
  isSingle?: boolean;
  isNavMini: boolean;
  disableLink?: boolean;
};

export const Logo = forwardRef<HTMLDivElement, LogoProps>(
  (
    {
      width,
      href = '/',
      height,
      isSingle = false,
      isNavMini,
      disableLink = false,
      className,
      sx,
      ...other
    },
    ref
  ) => {
    // const theme = useTheme();

    // const gradientId = useId();

    // const TEXT_PRIMARY = theme.vars.palette.text.primary;
    // const PRIMARY_LIGHT = theme.vars.palette.primary.light;
    // const PRIMARY_MAIN = theme.vars.palette.primary.main;
    // const PRIMARY_DARKER = theme.vars.palette.primary.dark;

    /*
    * OR using local (public folder)
    *
    const singleLogo = (
      <Box
        alt="Single logo"
        component="img"
        src={`${CONFIG.assetsDir}/logo/logo-single.svg`}
        width="100%"
        height="100%"
      />
    );

    const fullLogo = (
      <Box
        alt="Full logo"
        component="img"
        src={`${CONFIG.assetsDir}/logo/logo-full.svg`}
        width="100%"
        height="100%"
      />
    );
    *
    */

    const singleLogo = (
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 28 33"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0.105436 23.3274C-0.502185 25.595 1.61244 27.7276 3.98333 27.2291L11.2705 25.6916C12.1356 25.509 12.8679 24.9373 13.2548 24.1423L16.8146 16.8278L15.9099 24.7644C15.8048 25.6859 16.1523 26.601 16.8423 27.2206L22.3272 32.1444C24.1313 33.7616 27.0273 32.9778 27.6365 30.7043C27.7461 30.2951 27.7707 29.8632 27.7027 29.4379L23.3797 2.60912C22.9563 -0.00998379 19.5157 -0.931889 17.8395 1.12462L0.681329 22.1976C0.409635 22.5319 0.215058 22.9182 0.105436 23.3274Z"
          fill="#0A2FAF"
        />
      </svg>
    );

    const fullLogo = (
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 180 37"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M45.2447 37V25.3813C46.47 27.4093 49.005 28.719 51.8357 28.719C58.2999 28.719 62.1447 24.0716 62.1447 17.7341C62.1447 11.3966 58.4689 6.74915 52.0469 6.74915C49.0895 6.74915 46.5122 8.14339 45.2447 10.2559V7.08715H41.1465V37H45.2447ZM51.4554 25.1278C47.484 25.1278 44.9067 22.1703 44.9067 17.7341C44.9067 13.2978 47.484 10.3404 51.4554 10.3404C55.3847 10.3404 58.0042 13.2978 58.0042 17.7341C58.0042 22.1703 55.3847 25.1278 51.4554 25.1278Z"
          fill="#0D1F5F"
        />
        <path
          d="M74.4258 28.719C80.8055 28.719 85.3685 24.1138 85.3685 17.7341C85.3685 11.3544 80.8055 6.74915 74.4258 6.74915C68.0461 6.74915 63.4409 11.3544 63.4409 17.7341C63.4409 24.1138 68.0461 28.719 74.4258 28.719ZM74.4258 25.1278C70.3698 25.1278 67.6236 22.0858 67.6236 17.7341C67.6236 13.3823 70.3698 10.3404 74.4258 10.3404C78.4396 10.3404 81.1858 13.3823 81.1858 17.7341C81.1858 22.0858 78.4396 25.1278 74.4258 25.1278Z"
          fill="#0D1F5F"
        />
        <path
          d="M101.718 21.4098C101.718 25.7615 104.464 28.381 108.773 28.381H112.111V24.663H109.111C107.041 24.663 105.816 23.3956 105.816 21.1141V10.6361H112.111V7.08712H105.816V0H101.718V21.4098ZM99.8163 7.08712H97.6616C94.6618 7.08712 92.9718 8.35461 92.0001 11.1854V7.08712H87.9019V28.381H92.0001V18.7481C92.0001 14.1006 93.2676 10.6361 97.1546 10.6361H99.8163V7.08712Z"
          fill="#0D1F5F"
        />
        <path d="M118.995 28.381V0H114.897V28.381H118.995Z" fill="#0D1F5F" />
        <path
          d="M138.154 21.4521C137.563 23.8603 135.535 25.2123 132.408 25.2123C128.69 25.2123 126.071 22.4661 125.733 18.4523H141.999C142.041 18.0721 142.084 17.3961 142.084 16.8468C142.084 11.4388 138.577 6.74915 131.986 6.74915C125.437 6.74915 121.719 11.6501 121.719 17.6073C121.719 23.5223 125.817 28.719 132.408 28.719C137.647 28.719 141.492 25.846 142.253 21.4521H138.154ZM131.986 10.0446C135.493 10.0446 137.69 12.1993 137.943 15.4526H125.86C126.493 11.9036 128.648 10.0446 131.986 10.0446Z"
          fill="#0D1F5F"
        />
        <path
          d="M152.049 28.719C156.866 28.719 160.035 26.2263 160.035 22.3393C160.035 12.6218 147.867 18.0298 147.867 12.7486C147.867 11.1009 149.345 10.0024 151.542 10.0024C153.359 10.0024 155.725 10.9741 156.021 13.4246H159.866C159.654 9.41088 156.359 6.74915 151.542 6.74915C147.106 6.74915 143.98 9.19964 143.98 12.8753C143.98 21.9591 156.063 16.7623 156.063 22.4238C156.063 23.9871 154.5 25.2968 152.049 25.2968C149.303 25.2968 147.529 23.9026 147.275 21.4521H143.43C143.726 25.9305 147.064 28.719 152.049 28.719Z"
          fill="#0D1F5F"
        />
        <path
          d="M170.293 28.719C175.109 28.719 178.278 26.2263 178.278 22.3393C178.278 12.6218 166.11 18.0298 166.11 12.7486C166.11 11.1009 167.589 10.0024 169.786 10.0024C171.602 10.0024 173.968 10.9741 174.264 13.4246H178.109C177.898 9.41088 174.602 6.74915 169.786 6.74915C165.349 6.74915 162.223 9.19964 162.223 12.8753C162.223 21.9591 174.306 16.7623 174.306 22.4238C174.306 23.9871 172.743 25.2968 170.293 25.2968C167.546 25.2968 165.772 23.9026 165.518 21.4521H161.674C161.969 25.9305 165.307 28.719 170.293 28.719Z"
          fill="#0D1F5F"
        />
        <path
          d="M0.792353 26.6871C0.197938 28.9054 2.26661 30.9917 4.58597 30.504L11.7147 28.9999C12.561 28.8213 13.2774 28.262 13.6559 27.4843L17.1384 20.3287L16.2533 28.0929C16.1505 28.9943 16.4904 29.8896 17.1655 30.4957L22.5312 35.3125C24.296 36.8945 27.1291 36.1278 27.7251 33.9037C27.8323 33.5034 27.8564 33.0809 27.7898 32.6648L23.5608 6.41911C23.1466 3.85693 19.7808 2.95506 18.141 4.96688L1.35573 25.5818C1.08994 25.9089 0.899593 26.2868 0.792353 26.6871Z"
          fill="#0A2FAF"
        />
      </svg>
    );

    const baseSize = {
      width: width ?? 40,
      height: height ?? 40,
      ...(!isSingle && {
        width: width ?? 160,
        height: height ?? 37,
      }),
    };

    return (
      <Box
        ref={ref}
        component={RouterLink}
        href={href}
        className={logoClasses.root.concat(className ? ` ${className}` : '')}
        aria-label="Logo"
        sx={{
          ...baseSize,
          flexShrink: 0,
          display: 'inline-flex',
          verticalAlign: 'middle',
          ...(disableLink && { pointerEvents: 'none' }),
          ...sx,
        }}
        {...other}
      >
        {isNavMini ? singleLogo : fullLogo}
      </Box>
    );
  }
);
