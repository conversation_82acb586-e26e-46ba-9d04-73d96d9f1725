import { Iconify } from '~/components/iconify';
import { Label } from '~/components/label';
import { useGetSettings } from '~/utils/settings';

export function ProductStatusLabel({ quantity }: { quantity: number }) {
  const settings = useGetSettings();

  if (quantity == null) {
    return (
      <Label
        variant="soft"
        color="default"
        sx={{ borderRadius: 0.75 }}
        startIcon={<Iconify icon="eva:slash-outline" />}
      >
        Unavailable
      </Label>
    );
  } else if (quantity <= 0) {
    return (
      <Label
        variant="soft"
        color="error"
        sx={{ borderRadius: 0.75 }}
        startIcon={<Iconify icon="eva:minus-circle-outline" />}
      >
        Out of Stock
      </Label>
    );
  } else if (quantity <= settings.products.low_inventory_threshold) {
    return (
      <Label
        variant="soft"
        color="warning"
        sx={{ borderRadius: 0.75 }}
        startIcon={<Iconify icon="eva:arrowhead-down-outline" />}
      >
        Low Stock
      </Label>
    );
  } else {
    return (
      <Label
        variant="soft"
        color="success"
        sx={{ borderRadius: 0.75 }}
        startIcon={<Iconify icon="eva:checkmark-outline" />}
      >
        In Stock
      </Label>
    );
  }
}
