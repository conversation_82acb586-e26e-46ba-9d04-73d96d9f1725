import { InventoryStatus } from '@portless/models/types';

import { Iconify } from '~/components/iconify';
import { Label } from '~/components/label';

export function ProductInventoryStatusLabel({
  status,
}: {
  status: InventoryStatus;
}) {
  if (status === 'in_stock') {
    return (
      <Label
        variant="soft"
        color="success"
        sx={{ borderRadius: 0.75 }}
        startIcon={<Iconify icon="eva:checkmark-outline" />}
      >
        In Stock
      </Label>
    );
  } else if (status === 'low_stock') {
    return (
      <Label
        variant="soft"
        color="warning"
        sx={{ borderRadius: 0.75 }}
        startIcon={<Iconify icon="eva:arrowhead-down-outline" />}
      >
        Low Stock
      </Label>
    );
  } else if (status === 'out_of_stock') {
    return (
      <Label
        variant="soft"
        color="error"
        sx={{ borderRadius: 0.75 }}
        startIcon={<Iconify icon="eva:minus-circle-outline" />}
      >
        Out of Stock
      </Label>
    );
  } else {
    return (
      <Label
        variant="soft"
        color="default"
        sx={{ borderRadius: 0.75 }}
        startIcon={<Iconify icon="eva:slash-outline" />}
      >
        Unavailable
      </Label>
    );
  }
}
