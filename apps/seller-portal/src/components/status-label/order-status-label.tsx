import { Iconify } from '~/components/iconify';
import { Label } from '~/components/label';
import { VIEW_ORDER_EXCEPTIONS, getFeatureFlags } from '~/utils/feature-flags';

const labels = {
  '1': 'Pending',
  '2': 'In Progress',
  '3': 'Shipped',
  '4': 'Delivered',
  '5': 'Cancelled',
  '98': 'Exception',
  '99': 'Exception',
  '0': 'Exception',
};

export function OrderStatusLabel({
  status,
  exception,
}: {
  status: string;
  exception?: boolean;
}) {
  const keys = Object.keys(labels);

  if (!keys.includes(status)) {
    return (
      <Label
        variant="soft"
        color="default"
        sx={{ borderRadius: 0.75 }}
        startIcon={<Iconify icon="eva:slash-outline" />}
      >
        Unavailable
      </Label>
    );
  }
  const isViewOrderExceptionsEnabled =
    getFeatureFlags(VIEW_ORDER_EXCEPTIONS) ?? false;
  const exceptionActive = exception && isViewOrderExceptionsEnabled;

  const color =
    (exceptionActive && 'error') ||
    (status === '2' && 'info') ||
    (status === '3' && 'success') ||
    (status === '4' && 'primary') ||
    (status === '5' && 'error') ||
    (status === '98' && 'error') ||
    (status === '99' && 'error') ||
    'default';

  const icon =
    (exceptionActive && 'eva:alert-circle-outline') ||
    (status === '2' && 'eva:clock-outline') ||
    (status === '3' && 'eva:checkmark-outline') ||
    (status === '4' && 'eva:checkmark-outline') ||
    (status === '5' && 'eva:minus-circle-outline') ||
    (status === '98' && 'eva:minus-circle-outline') ||
    (status === '99' && 'eva:minus-circle-outline') ||
    'eva:slash-outline';

  return (
    <Label
      variant="soft"
      color={color}
      sx={{ borderRadius: 0.75 }}
      startIcon={<Iconify icon={icon} />}
    >
      {exceptionActive ? labels['0'] : labels[status] ?? 'Unavailable'}
    </Label>
  );
}
