import {
  collection,
  CollectionReference,
  Firestore,
  getDocs,
  orderBy,
  query,
  where,
} from 'firebase/firestore';
import { GetListParams, GetListResult, RaRecord } from 'react-admin';

export default async <R extends RaRecord<string>>(
  resource: string,
  params: GetListParams,
  db: Firestore,
  organizationId: string
): Promise<GetListResult<R & { id: string }>> => {
  let collectionRef: CollectionReference;

  switch (resource) {
    case 'bundles':
      collectionRef = collection(db, `organizations/${organizationId}/bundles`);
      break;
    case 'products':
      collectionRef = collection(
        db,
        `organizations/${organizationId}/products`
      );
      break;
    case 'inventory':
      collectionRef = collection(
        db,
        `organizations/${organizationId}/products`
      );
      break;
    case 'orders':
      collectionRef = collection(db, `organizations/${organizationId}/orders`);
      break;
    case 'users':
      collectionRef = collection(db, `organizations/${organizationId}/users`);
      break;
    case 'locations':
      collectionRef = collection(
        db,
        `organizations/${organizationId}/locations`
      );
      break;
    case 'organizations':
      collectionRef = collection(db, 'organizations');
      break;
    default:
      throw new Error(`Unknown resource type ${resource}`);
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const queryConstraints: any[] = [];

  if (params.sort && params.sort.field !== 'id') {
    // don't sort by id since it's a generated field
    queryConstraints.push(
      orderBy(params.sort.field, params.sort.order === 'DESC' ? 'desc' : 'asc')
    );
  }

  if (params?.filter?.filterField && params?.filter?.filterValue) {
    queryConstraints.push(
      where(params?.filter?.filterField, '==', params?.filter?.filterValue)
    );
  }

  const querySnapshot = await getDocs(
    query(collectionRef, ...queryConstraints)
  );
  const data = querySnapshot.docs.map<R & { id: string }>((snap) => ({
    ...(snap.data() as R),
    id: snap.id,
  }));

  if (data.length && resource === 'organizations') {
    return {
      data: data.filter((item) => item.id === organizationId),
      total: 1,
    };
  }

  return {
    data, // paginate on client-side
    total: querySnapshot.size,
  };
};
