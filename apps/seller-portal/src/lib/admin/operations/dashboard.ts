import {
  collection,
  doc,
  Firestore,
  getCountFromServer,
  getDoc,
  getDocs,
  query,
  where,
} from 'firebase/firestore';
import { GetListParams } from 'react-admin';
import { startOfYear } from 'date-fns/startOfYear';
import { endOfYear } from 'date-fns/endOfYear';
import { endOfMonth } from 'date-fns/endOfMonth';
import { eachMonthOfInterval } from 'date-fns/eachMonthOfInterval';
import { getExceptionsText } from '~/utils/exceptions-utils';

export default async (
  params: GetListParams,
  db: Firestore,
  organizationId: string
) => {
  const data: any[] = [];

  // get low inventory threshold
  const orgSnap = await getDoc(doc(db, `organizations/${organizationId}`));
  const org = orgSnap.data();

  // get products and inventory
  const productsRef = collection(
    db,
    `organizations/${organizationId}/products`
  );

  const lowInventoryQuery = query(
    productsRef,
    where('total_inventory.available', '>', 0),
    where(
      'total_inventory.available',
      '<=',
      org?.settings?.products?.low_inventory_threshold ?? 0
    )
  );

  const lowInventorySnap = await getCountFromServer(lowInventoryQuery);
  const lowInventoryCount = lowInventorySnap.data().count;
  // console.log('low inventory', lowInventoryCount);

  data.push({
    id: 'low_stock',
    value: lowInventoryCount,
  });

  const noInventoryQuery = query(
    productsRef,
    where('total_inventory.available', '<=', 0)
  );

  const noInventorySnap = await getCountFromServer(noInventoryQuery);
  const noInventoryCount = noInventorySnap.data().count;
  // console.log('no inventory', noInventoryCount);

  data.push({
    id: 'out_of_stock',
    value: noInventoryCount,
  });

  // get orders
  const ordersRef = collection(db, `organizations/${organizationId}/orders`);
  const start = startOfYear(new Date());
  const end = endOfYear(start);
  const months = eachMonthOfInterval({
    start,
    end,
  });

  const ordersByMonth = await Promise.all(
    months.map((month) => {
      const monthStart = month;
      const monthEnd = endOfMonth(monthStart);

      const ordersQuery = query(
        ordersRef,
        where('created_at', '>=', monthStart.toISOString()),
        where('created_at', '<', monthEnd.toISOString())
      );
      return getCountFromServer(ordersQuery).then((snap) => snap.data().count);
    })
  );

  data.push({
    id: 'orders_by_month',
    value: ordersByMonth,
  });

  const ordersWithExceptionsQuery = query(
    ordersRef,
    where('has_exceptions', '==', true)
  );
  const ordersWithExceptionsSnap = await getDocs(ordersWithExceptionsQuery);

  const orderWithExceptions = ordersWithExceptionsSnap.docs.map((orderSnap) => {
    const order = orderSnap.data();
    return {
      id: orderSnap.id,
      extId: order.ext_id,
      customerName: order?.customer?.name ?? '',
      exceptions: getExceptionsText(
        order.fulfillments
          .filter((f) => Array.isArray(f.exceptions))
          .map((f) => f.exceptions)
          .flat()
      ),
    };
  });

  data.push({
    id: 'orders_with_exceptions_count',
    value: ordersWithExceptionsSnap.size,
  });

  data.push({
    id: 'orders_with_exceptions',
    value: orderWithExceptions,
  });

  // const ordersRef = getFirelord<OrderMeta>(
  //   db,
  //   'organizations',
  //   'orders'
  // ).collection(organizationId);

  // // eslint-disable-next-line @typescript-eslint/no-explicit-any
  // const queryConstraints: any[] = [];

  // if (params.sort && params.sort.field !== 'id') {
  //   // don't sort by id since it's a generated field
  //   queryConstraints.push(
  //     orderBy(params.sort.field, params.sort.order === 'DESC' ? 'desc' : 'asc')
  //   );
  // }

  // if (params?.filter?.filterField && params?.filter?.filterValue) {
  //   queryConstraints.push(
  //     where(params?.filter?.filterField, '==', params?.filter?.filterValue)
  //   );
  // }

  // const querySnapshot = await getDocs(query(col, ...queryConstraints));
  // const data = querySnapshot.docs.map<R & { id: string }>((snap) => ({
  //   id: snap.id,
  //   ...(snap.data() as R),
  // }));

  // if (data.length && resource === 'organizations') {
  //   return {
  //     data: data.filter((item) => item.id === organizationId),
  //     total: 1,
  //   };
  // }

  return {
    data,
    total: data.length,
  };
};
