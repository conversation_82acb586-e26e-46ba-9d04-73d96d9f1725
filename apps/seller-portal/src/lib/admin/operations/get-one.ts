import { doc, DocumentReference, Firestore, getDoc } from 'firebase/firestore';
import { GetOneParams, GetOneResult, RaRecord } from 'react-admin';
import { Order } from '@portless/core';

export default async <R extends RaRecord<string>>(
  resource: string,
  params: GetOneParams,
  db: Firestore,
  organizationId: string
): Promise<GetOneResult<R>> => {
  let documentRef: DocumentReference;

  const organizationsRef = doc(db, `organizations/${organizationId}`);

  switch (resource) {
    case 'bundles':
      documentRef = doc(organizationsRef, `bundles/${params.id}`);
      break;
    case 'products':
      documentRef = doc(organizationsRef, `products/${params.id}`);
      break;
    case 'orders':
      documentRef = doc(organizationsRef, `orders/${params.id}`);
      break;
    case 'fulfillments':
      documentRef = doc(organizationsRef, `orders/${params.id}`);
      break;
    default:
      throw new Error(`Unknown resource type ${resource}`);
  }

  const snap = await getDoc(documentRef);
  if (!snap.exists()) {
    throw new Error(`Resource ${resource} with id ${params.id} not found`);
  }

  const data = { ...(snap.data() as R), id: snap.id };

  if (resource === 'fulfillments') {
    // append tracking info
    const order = snap.data() as Order;
    if (order.fulfillments && order.fulfillments.length > 0) {
      const fulfillment = order.fulfillments[order.fulfillments.length - 1];

      if (fulfillment) {
        const orderId = order.int_id;
        try {
          const response = await fetch(
            `/api/organization/${organizationId}/tracking-status/${orderId}`
          );
          const tracking = await response.json();
          fulfillment.shipment.status =
            tracking.track_info.latest_status.status;
          fulfillment.shipment.metrics = tracking.track_info.time_metrics;
          fulfillment.shipment.milestone = tracking.track_info.milestone;
          fulfillment.shipment.tracking =
            tracking.track_info.tracking.providers;
        } catch (error) {
          console.error(error);
        }

        return {
          data: {
            ...fulfillment,
            id: order.int_id,
          } as unknown as R,
        };
      }
    }

    throw new Error('Fulfillment not found');
  }

  return {
    data,
  };
};
