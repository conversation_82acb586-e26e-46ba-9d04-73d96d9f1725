import { http } from '../../../http';

export async function updateProduct(
  organization_id: string,
  product_id: string,
  custom_code: string,
  declared_value: number
) {
  const url = `organizations/${organization_id}/products/${product_id}`;
  const response = await http.put(url, { custom_code, declared_value });

  if (response.status !== 200) {
    throw new Error('Failed to update the product');
  }

  return { data: response.data };
}
