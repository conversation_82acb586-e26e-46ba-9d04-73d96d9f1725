import {
  collection,
  doc,
  DocumentReference,
  Firestore,
  getDoc,
} from 'firebase/firestore';
import { GetManyParams, GetManyResult, RaRecord } from 'react-admin';

export default async <R extends RaRecord<string>>(
  resource: string,
  params: GetManyParams,
  db: Firestore,
  organizationId: string
): Promise<GetManyResult<R>> => {
  let docsRef: DocumentReference[];

  if (resource === 'bundles') {
    const collectionRef = collection(
      db,
      `organizations/${organizationId}/bundles`
    );
    docsRef = params.ids.map((id) => doc(collectionRef, id));
  } else if (resource === 'products') {
    const collectionRef = collection(
      db,
      `organizations/${organizationId}/products`
    );
    docsRef = params.ids.map((id) => doc(collectionRef, id));
  } else if (resource === 'locations') {
    const collectionRef = collection(
      db,
      `organizations/${organizationId}/locations`
    );
    docsRef = params.ids.map((id) => doc(collectionRef, id));
  } else {
    throw new Error(`Unknown resource type ${resource}`);
  }

  const snaps = await Promise.all(docsRef.map((doc) => getDoc(doc)));
  const data = snaps.map((snap) => ({ ...(snap.data() as R), id: snap.id }));

  return {
    data,
  };
};
