import { GetListParams, GetListResult } from 'react-admin';
import { SearchParams } from 'typesense/lib/Typesense/Documents';
import { Organization } from '@portless/core';
import { createClient } from '~/lib/typesense';
import { VIEW_ORDER_EXCEPTIONS, getFeatureFlags } from '~/utils/feature-flags';

const inventorySortingFields = ['on_hand', 'committed', 'available', 'status'];

export const search = async (
  resource: string,
  params: GetListParams,
  organization: Organization
) => {
  const searchParams: SearchParams = {
    infix: 'always', // allow substring searches
  };

  if (params.pagination) {
    searchParams['page'] = params.pagination.page;
    searchParams['per_page'] = params.pagination.perPage;
  }

  if (params.sort) {
    if (params.sort.field !== 'id') {
      if (
        resource === 'products' &&
        inventorySortingFields.includes(params.sort.field)
      ) {
        searchParams['sort_by'] = `inventory.${
          params.sort.field
        }:${params.sort.order.toLowerCase()}`;
      } else
        searchParams['sort_by'] = `${
          params.sort.field
        }:${params.sort.order.toLowerCase()}`;
    }
  }

  // products
  if (resource === 'products') {
    if (params.filter) {
      searchParams['q'] = params.filter.q ?? '*';
      searchParams['query_by'] = ['sku', 'name']; // order is important

      if (params.filter.stock && params.filter.stock !== 'all') {
        searchParams['filter_by'] = `inventory.status:${params.filter.stock}`;
      }
    }

    searchParams.facet_by = 'inventory.status';
  } else if (resource === 'orders') {
    const isViewOrderExceptionsEnabled =
      getFeatureFlags(VIEW_ORDER_EXCEPTIONS) ?? false;
    if (params.filter) {
      searchParams['q'] = params.filter.q ?? '*';
      searchParams['query_by'] = ['ext_id', 'customer.name', 'customer.email']; // order is important
    }

    if (params.filter.status && params.filter.status !== 'all') {
      if (params.filter.status === '0' && isViewOrderExceptionsEnabled) {
        searchParams['filter_by'] = `has_exceptions:true`;
      } else {
        searchParams['filter_by'] = `status:${params.filter.status}`;
      }
    }

    const facet_by = ['status'];

    if (isViewOrderExceptionsEnabled) {
      facet_by.push('has_exceptions');
    }
    searchParams.facet_by = facet_by.join(',');
  }

  const typesense = createClient(organization.settings.search.token);
  const results = await typesense
    .collections(resource)
    .documents()
    .search(searchParams, {});

  const result: GetListResult = {
    data: results.hits?.map((hit) => hit.document) as any,
    total: results.found,
  };

  if (resource === 'products') {
    result.meta = {
      statuses: results.facet_counts?.find(
        (facet) => facet.field_name === 'inventory.status'
      )?.counts,
    };
  } else if (resource === 'orders') {
    result.meta = {
      statuses: results.facet_counts?.find(
        (facet) => facet.field_name === 'status'
      )?.counts,
      has_exceptions: results.facet_counts
        ?.find((facet) => facet.field_name === 'has_exceptions')
        ?.counts.filter((count) => count.value === 'true'),
    };
  }

  return result;
};
