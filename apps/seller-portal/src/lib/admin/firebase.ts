import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getAuthProvider } from './auth-provider';
import { getDataProvider } from './data-provider';
import devConfig from './dev-config';

console.assert(import.meta.env.VITE_FIREBASE_CONFIG);
const app = initializeApp(JSON.parse(import.meta.env.VITE_FIREBASE_CONFIG));

// connect to local emulator during development
devConfig(app);

export const auth = getAuth(app);
export const db = getFirestore(app);

export const authProvider = getAuthProvider(auth);
export const dataProvider = getDataProvider(auth, db);
