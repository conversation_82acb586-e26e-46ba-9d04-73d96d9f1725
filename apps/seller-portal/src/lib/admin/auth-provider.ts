import { Auth, signInWithEmailAndPassword, signOut } from 'firebase/auth';
import { AuthProvider as Ra<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-admin';
import { db } from './firebase';
import { getDataProvider } from './data-provider';
import posthog from 'posthog-js';

export const getAuthProvider = (auth: Auth): RaAuthProvider => ({
  login: async ({ username, password }) => {
    const { user } = await signInWithEmailAndPassword(auth, username, password);

    const idToken = await auth.currentUser?.getIdTokenResult();
    posthog.identify(user.uid, {
      email: user.email,
      organization_id: idToken?.claims.organization_id,
      role: idToken?.claims.role,
    });
    localStorage.setItem('profile', JSON.stringify(user));
  },
  logout: async () => {
    await signOut(auth);
    posthog.reset();
    localStorage.removeItem('profile');
  },
  checkAuth: async () => {
    await auth.authStateReady();
    return auth.currentUser ? Promise.resolve() : Promise.reject();
  },
  checkError: (error) => {
    if (error.code === 400 || error.code === 403) {
      return Promise.reject();
    }

    return Promise.resolve();
  },
  getIdentity: async () => {
    if (auth.currentUser) {
      const storedProfile = localStorage.getItem('profile');
      if (storedProfile) {
        return Promise.resolve(JSON.parse(storedProfile));
      }
    }
    return Promise.reject();
  },
  getPermissions: async () => {
    // get claims
    const idToken = await auth.currentUser?.getIdTokenResult();
    return idToken?.claims.role;
  },
  updateUserProfile: async (email: string) => {
    const dataProvider = getDataProvider(auth, db);

    const { data } = await dataProvider.getList('users', {
      filter: { filterField: 'email', filterValue: email },
    });
    if (data[0]?.name) {
      localStorage.setItem('profile', JSON.stringify(data[0]));
    }
  },
});
