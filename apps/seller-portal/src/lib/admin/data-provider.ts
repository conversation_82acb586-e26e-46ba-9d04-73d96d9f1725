import { Auth } from 'firebase/auth';
import { doc, Firestore, getDoc } from 'firebase/firestore';
import {
  DataProvider,
  GetListResult,
  GetManyResult,
  GetOneResult,
} from 'react-admin';
import getList from './operations/get-list';
import getMany from './operations/get-many';
import getOne from './operations/get-one';
import { search } from './operations/search';
import dashboard from './operations/dashboard';
import { updateProduct } from './operations/products/update-product';
import { Organization } from '@portless/core';

export const getDataProvider = (auth: Auth, db: Firestore): DataProvider => ({
  getList: async (resource, params): Promise<GetListResult> => {
    await auth.authStateReady();

    // get auth token
    const token = await auth.currentUser?.getIdTokenResult();
    const organizationId = token?.claims.organization_id as string;

    // get organization
    const docSnap = await getDoc(doc(db, `organizations/${organizationId}`));
    const organization = docSnap.data();

    if (!organization) {
      throw new Error(
        `Error fetching organization doc for org ${organizationId}`
      );
    }

    if (resource === 'products' || resource === 'orders') {
      return search(resource, params, organization as Organization);
    }

    if (resource === 'dashboard') {
      return dashboard(params, db, organizationId);
    }

    return getList(resource, params, db, organizationId);
  },
  getOne: async (resource, params): Promise<GetOneResult> => {
    await auth.authStateReady();

    const token = await auth.currentUser?.getIdTokenResult();
    const organizationId = token?.claims.organization_id as string;

    return getOne(resource, params, db, organizationId);
  },
  getMany: async (resource, params): Promise<GetManyResult> => {
    await auth.authStateReady();

    const token = await auth.currentUser?.getIdTokenResult();
    const organizationId = token?.claims.organization_id as string;

    return getMany(resource, params, db, organizationId);
  },
  getManyReference: (): never => {
    throw new Error('Function not implemented.');
  },
  update: async (resource, params): Promise<GetOneResult | never> => {
    if (resource === 'products') {
      const token = await auth.currentUser?.getIdTokenResult();
      const organizationId = token?.claims.organization_id as string;
      const { id, custom_code, declared_value } = params.data;
      return updateProduct(organizationId, id, custom_code, declared_value);
    }
    throw new Error('Function not implemented.');
  },
  updateMany: (): never => {
    throw new Error('Function not implemented.');
  },
  create: (): never => {
    throw new Error('Function not implemented.');
  },
  delete: (): never => {
    throw new Error('Function not implemented.');
  },
  deleteMany: (): never => {
    throw new Error('Function not implemented.');
  },
});
