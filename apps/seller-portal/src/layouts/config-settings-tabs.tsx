import { Resource } from 'react-admin';

import { LocationList } from '~/sections/settings/view/list-views/locations-list-view';
import { UserListView } from '~/sections/user/view';

import { ICONS } from './config-icons';

export const settingsTabs = [
  {
    id: 'user',
    title: 'User',
    icon: ICONS.user,
    component: <Resource name="users" list={UserListView} />,
  },
  {
    id: 'locations',
    title: 'Locations',
    icon: ICONS.tour,
    component: <Resource name="locations" list={LocationList} />,
  },
];
