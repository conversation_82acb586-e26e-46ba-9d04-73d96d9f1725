import { beforeAll, beforeEach, describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { defaultSettings, SettingsProvider } from '~/components/settings';
import { ThemeProvider } from '~/theme/theme-provider';
import { navData } from '../config-nav-dashboard';
import { NavVertical } from './nav-vertical';

describe('NavVertical', () => {
  beforeAll(() => {
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(), // Deprecated
        removeListener: vi.fn(), // Deprecated
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });

    Object.defineProperty(window, 'getComputedStyle', {
      writable: true,
      value: vi.fn().mockImplementation((element, pseudoElt) => ({
        content: '',
        getPropertyValue: vi.fn(),
      })),
    });
  });

  beforeEach(() => {
    render(
      <BrowserRouter>
        <SettingsProvider settings={defaultSettings}>
          <ThemeProvider>
            <NavVertical
              data={navData}
              isNavMini={false}
              layoutQuery="lg"
              onToggleNav={() => vi.fn()}
            />
          </ThemeProvider>
        </SettingsProvider>
      </BrowserRouter>
    );
  });

  it('should render correctly', () => {
    navData[0]!.items.forEach((item) => {
      expect(screen.queryByText(item.title)).toBeDefined();
    });
  });
});
