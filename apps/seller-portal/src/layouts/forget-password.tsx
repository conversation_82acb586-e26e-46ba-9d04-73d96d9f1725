import { z as zod } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';

import { paths } from '~/routes/paths';
import { RouterLink } from '~/routes/components';

import { PasswordIcon } from '~/assets/icons';
import { Iconify } from '~/components/iconify';
import { Form, RHFTextField } from '~/components/hook-form';
import { sendPasswordResetEmail } from 'firebase/auth';
import { auth } from '~/lib/admin/firebase';
import { useForm } from 'react-hook-form';
import { useState } from 'react';

export default function ForgotPasswordView() {
  const [resetLinkSent, setResetLinkSent] = useState<boolean>(false);
  const ForgotPasswordSchema = zod.object({
    email: zod
      .string()
      .min(1, { message: 'Email is required!' })
      .email({ message: 'Email must be a valid email address!' }),
  });

  const defaultValues = {
    email: '',
  };

  const methods = useForm({
    resolver: zodResolver(ForgotPasswordSchema),
    defaultValues,
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      const { email } = data;
      await sendPasswordResetEmail(auth, email);
      setResetLinkSent(true);
    } catch (error) {
      console.error(error);
    }
  });

  const renderReturnLink = (
    <Link
      component={RouterLink}
      href={paths.auth.login}
      color="inherit"
      variant="subtitle2"
      sx={{
        alignItems: 'center',
        display: 'inline-flex',
      }}
    >
      <Iconify icon="eva:arrow-ios-back-fill" width={16} />
      Return to sign in
    </Link>
  );

  const renderForm = (
    <Stack spacing={3} alignItems="center">
      <RHFTextField name="email" label="Email address" />

      <LoadingButton
        fullWidth
        size="large"
        type="submit"
        variant="contained"
        loading={isSubmitting}
      >
        Send Request
      </LoadingButton>
      {renderReturnLink}
    </Stack>
  );

  const renderHead = (
    <>
      <PasswordIcon sx={{ height: 96 }} />

      <Stack spacing={1} sx={{ mt: 3, mb: 5 }}>
        <Typography variant="h3">Forgot your password?</Typography>

        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          Please enter the email address associated with your account and We
          will email you a link to reset your password.
        </Typography>
      </Stack>
    </>
  );

  const renderMessage = (
    <>
      <PasswordIcon sx={{ height: 96 }} />

      <Stack spacing={1} sx={{ mt: 3, mb: 5 }} alignItems="center">
        <Typography variant="h3">Reset Link Sent</Typography>

        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          Please check your email for password recovery link.
        </Typography>

        {renderReturnLink}
      </Stack>
    </>
  );

  return (
    <>
      {resetLinkSent ? (
        renderMessage
      ) : (
        <>
          {renderHead}
          <Form methods={methods} onSubmit={onSubmit}>
            {renderForm}
          </Form>
        </>
      )}
    </>
  );
}
