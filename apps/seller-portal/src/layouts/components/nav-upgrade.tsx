import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import type { StackProps } from '@mui/material/Stack';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { alpha as hexAlpha } from '@mui/material/styles';
import { m } from 'framer-motion';
import { useGetIdentity } from 'react-admin';

import { Label } from '~/components/label';
import { CONFIG } from '~/config-global';
import { useGetOrganization } from '~/lib/admin/organization-context';
import { bgGradient, varAlpha } from '~/theme/styles';

export function NavUpgrade({ sx, ...other }: StackProps) {
  const { data: user, isPending } = useGetIdentity();
  const organization = useGetOrganization();

  if (isPending || !organization) {
    return null;
  }

  const {
    settings: { account },
  } = organization;

  return (
    <Stack
      sx={{
        px: 2,
        py: 2,
        textAlign: 'center',
        ...sx,
      }}
      {...other}
    >
      <Stack
        alignItems="center"
        sx={{
          px: 1,
          py: 3,
          backgroundColor: '#F7F7F8',
          borderRadius: 1,
          ...sx,
        }}
      >
        <Box sx={{ position: 'relative' }}>
          <Avatar
            src={user?.photoURL}
            alt={account?.account_manager_name}
            sx={{ width: 48, height: 48 }}
          >
            {account?.account_manager_name?.charAt(0).toUpperCase()}
          </Avatar>

          <Label
            color={account ? 'success' : 'default'}
            variant="inverted"
            sx={{
              top: -6,
              px: 0.5,
              left: 40,
              height: 20,
              position: 'absolute',
              borderBottomLeftRadius: 2,
            }}
          >
            {account ? 'Online' : 'Offline'}
          </Label>
        </Box>

        <Stack spacing={0.5} sx={{ mb: 2, mt: 1.5, width: 1 }}>
          <Typography
            variant="subtitle2"
            noWrap
            sx={{ color: 'var(--layout-nav-text-primary-color)' }}
          >
            {account?.account_manager_name || 'Support Agent'}
          </Typography>

          <Typography
            variant="body2"
            noWrap
            sx={{ color: 'var(--layout-nav-text-disabled-color)' }}
          >
            Have a question? We're here to help
          </Typography>
        </Stack>

        <Button
          variant="outlined"
          href={`mailto:${account?.account_manager_email}`}
          target="_blank"
          rel="noopener"
          disabled={!account?.account_manager_email}
        >
          Chat with {`${account?.account_manager_name || 'Portless'} `}
        </Button>
      </Stack>
    </Stack>
  );
}

// ----------------------------------------------------------------------

export function UpgradeBlock({ sx, ...other }: StackProps) {
  return (
    <Stack
      sx={{
        ...bgGradient({
          color: `135deg, ${hexAlpha('#F7BB95', 0.92)}, ${hexAlpha(
            '#5B2FF3',
            0.92
          )}`,
          imgUrl: `${CONFIG.assetsDir}/assets/background/background-7.webp`,
        }),
        px: 3,
        py: 4,
        borderRadius: 2,
        position: 'relative',
        ...sx,
      }}
      {...other}
    >
      <Box
        sx={{
          top: 0,
          left: 0,
          width: 1,
          height: 1,
          borderRadius: 2,
          position: 'absolute',
          border: (theme) =>
            `solid 3px ${varAlpha(
              theme.vars.palette.common.whiteChannel,
              0.16
            )}`,
        }}
      />

      <Box
        component={m.img}
        animate={{ y: [12, -12, 12] }}
        transition={{
          duration: 8,
          ease: 'linear',
          repeat: Infinity,
          repeatDelay: 0,
        }}
        alt="Small Rocket"
        src={`${CONFIG.assetsDir}/assets/illustrations/illustration-rocket-small.webp`}
        sx={{ right: 0, width: 112, height: 112, position: 'absolute' }}
      />

      <Stack alignItems="flex-start" sx={{ position: 'relative' }}>
        <Box component="span" sx={{ typography: 'h5', color: 'common.white' }}>
          35% OFF
        </Box>

        <Box
          component="span"
          sx={{
            mb: 2,
            mt: 0.5,
            color: 'common.white',
            typography: 'subtitle2',
          }}
        >
          Power up Productivity!
        </Box>

        <Button variant="contained" size="small" color="warning">
          Upgrade to Pro
        </Button>
      </Stack>
    </Stack>
  );
}
