import { useGetIdentity } from 'react-admin';

import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import type { IconButtonProps } from '@mui/material/IconButton';
import IconButton from '@mui/material/IconButton';
import MenuItem from '@mui/material/MenuItem';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';

import { AnimateAvatar } from '~/components/animate';
import { Iconify } from '~/components/iconify';
import { Label } from '~/components/label';
import { Scrollbar } from '~/components/scrollbar';
import { useBoolean } from '~/hooks/use-boolean';
import { usePathname } from '~/routes/hooks';
import { paths } from '~/routes/paths';
import { varAlpha } from '~/theme/styles';

import { AccountButton } from './account-button';
import { SignOutButton } from './sign-out-button';
import { Organization } from '@portless/core';
import { useGetOrganization } from '~/lib/admin/organization-context';
import { useEffect, useState } from 'react';
import { auth } from '~/lib/admin/firebase';

// ----------------------------------------------------------------------

export type AccountDrawerProps = IconButtonProps & {
  data?: {
    label: string;
    href: string;
    icon?: React.ReactNode;
    info?: React.ReactNode;
  }[];
};

export function AccountDrawer({ data = [], sx, ...other }: AccountDrawerProps) {
  const [organization, setOrganization] = useState<Organization | undefined>(
    undefined
  );

  const organizationData = useGetOrganization();

  const theme = useTheme();
  const pathname = usePathname();
  const { identity, isPending } = useGetIdentity();
  const {
    value: open,
    onTrue: handleOpenDrawer,
    onFalse: handleCloseDrawer,
  } = useBoolean(false);

  useEffect(() => {
    if (organizationData) setOrganization(organizationData);
  }, [auth.currentUser, organizationData]);

  const renderAvatar = (
    <AnimateAvatar
      width={96}
      slotProps={{
        avatar: {
          src: identity?.avatar ?? '',
          alt: identity?.name,
        },
        overlay: {
          border: 2,
          spacing: 3,
          color: `linear-gradient(135deg, ${varAlpha(
            theme.vars.palette.primary.mainChannel,
            0
          )} 25%, ${theme.vars.palette.primary.main} 100%)`,
        },
      }}
    >
      {identity?.name?.charAt(0).toUpperCase()}
    </AnimateAvatar>
  );

  if (isPending) {
    return null;
  }

  return (
    <>
      <AccountButton
        onClick={handleOpenDrawer}
        photoURL={identity?.avatar ?? ''}
        displayName={identity?.name}
        sx={sx}
        {...other}
      />

      <Drawer
        open={open}
        onClose={handleCloseDrawer}
        anchor="right"
        slotProps={{ backdrop: { invisible: true } }}
        PaperProps={{ sx: { width: 320 } }}
      >
        <IconButton
          onClick={handleCloseDrawer}
          sx={{ top: 12, left: 12, zIndex: 9, position: 'absolute' }}
        >
          <Iconify icon="mingcute:close-line" />
        </IconButton>

        <Scrollbar>
          <Stack alignItems="center" sx={{ pt: 8 }}>
            {renderAvatar}

            <Typography variant="subtitle1" noWrap sx={{ mt: 2 }}>
              {identity?.name}
            </Typography>

            <Typography sx={{ color: 'text.secondary' }} variant="body2">
              {identity?.email}
            </Typography>

            <Typography
              sx={{ color: 'text.primary', marginTop: '1rem' }}
              variant="subtitle2"
            >
              {organization?.name}
            </Typography>
          </Stack>

          {data && data.length > 0 && (
            <Stack
              sx={{
                py: 3,
                px: 2.5,
                borderTop: `dashed 1px ${theme.vars.palette.divider}`,
                borderBottom: `dashed 1px ${theme.vars.palette.divider}`,
              }}
            >
              {data.map((option) => {
                const rootLabel = pathname.includes('/dashboard')
                  ? 'Home'
                  : 'Dashboard';

                const rootHref = pathname.includes('/dashboard')
                  ? '/'
                  : paths.dashboard.root;

                return (
                  <MenuItem
                    key={option.label}
                    onClick={() =>
                      handleClickItem(
                        option.label === 'Home' ? rootHref : option.href
                      )
                    }
                    sx={{
                      py: 1,
                      color: 'text.secondary',
                      '& svg': { width: 24, height: 24 },
                      '&:hover': { color: 'text.primary' },
                    }}
                  >
                    {option.icon}

                    <Box component="span" sx={{ ml: 2 }}>
                      {option.label === 'Home' ? rootLabel : option.label}
                    </Box>

                    {option.info && (
                      <Label color="error" sx={{ ml: 1 }}>
                        {option.info}
                      </Label>
                    )}
                  </MenuItem>
                );
              })}
            </Stack>
          )}
        </Scrollbar>

        <Box sx={{ p: 2.5 }}>
          <SignOutButton onClose={handleCloseDrawer} />
        </Box>
      </Drawer>
    </>
  );
}
