import { useCallback } from 'react';
import { useLogout } from 'react-admin';

import type { ButtonProps } from '@mui/material/Button';
import Button from '@mui/material/Button';
import type { SxProps, Theme } from '@mui/material/styles';

// ----------------------------------------------------------------------

type Props = ButtonProps & {
  sx?: SxProps<Theme>;
  onClose?: () => void;
};

export function SignOutButton({ onClose, ...other }: Props) {
  const logout = useLogout();
  const handleLogout = useCallback(async () => {
    try {
      await logout();
      onClose?.();
    } catch (error) {
      console.error(error);
    }
  }, [logout, onClose]);

  return (
    <Button
      fullWidth
      variant="soft"
      size="large"
      color="error"
      onClick={handleLogout}
      {...other}
    >
      Logout
    </Button>
  );
}
