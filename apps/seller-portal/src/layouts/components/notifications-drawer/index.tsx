import Badge from '@mui/material/Badge';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import type { IconButtonProps } from '@mui/material/IconButton';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import SvgIcon from '@mui/material/SvgIcon';
import Typography from '@mui/material/Typography';
import { m } from 'framer-motion';
import { useCreatePath, useGetList } from 'react-admin';

import { varHover } from '~/components/animate';
import { Iconify } from '~/components/iconify';
import { Scrollbar } from '~/components/scrollbar';
import { useBoolean } from '~/hooks/use-boolean';
import { getFeatureFlags, VIEW_ORDER_EXCEPTIONS } from '~/utils/feature-flags';

import type { NotificationItemProps } from './notification-item';
import { NotificationItem } from './notification-item';

// ----------------------------------------------------------------------

export type NotificationsDrawerProps = IconButtonProps & {
  data?: NotificationItemProps[];
};

export function NotificationsDrawer({
  sx,
  ...other
}: NotificationsDrawerProps) {
  const drawer = useBoolean();
  const createPath = useCreatePath();

  const { data, isPending } = useGetList('dashboard');

  const isViewOrderExceptionsEnabled =
    getFeatureFlags(VIEW_ORDER_EXCEPTIONS) ?? false;

  const totalOutOfStock = data?.find((item) => item.id === 'out_of_stock');
  const totalLowStock = data?.find((item) => item.id === 'low_stock');
  const orderWithExceptions = data?.find(
    (item) => item.id === 'orders_with_exceptions'
  );

  const notifications: NotificationItemProps[] = [];

  if (totalOutOfStock?.value > 0) {
    notifications.push({
      id: 'out-of-stock',
      category: 'products',
      title: `${totalOutOfStock?.value} items out of stock`,
      type: 'product',
      isUnRead: true,
      url: createPath({ type: 'list', resource: 'products' }),
    });
  }

  if (totalLowStock?.value > 0) {
    notifications.push({
      id: 'low-stock',
      category: 'products',
      title: `${totalLowStock?.value} items have low inventory`,
      type: 'product',
      isUnRead: true,
      url: createPath({ type: 'list', resource: 'products' }),
    });
  }

  if (isViewOrderExceptionsEnabled && orderWithExceptions?.value?.length > 0) {
    orderWithExceptions.value?.forEach((item) =>
      notifications.push({
        id: `order-exception-${item.id}`,
        category: item.exceptions,
        title: `Order #${item?.extId}`,
        type: 'order',
        isUnRead: true,
        url: createPath({ type: 'show', id: item.id, resource: 'orders' }),
        isError: true,
      })
    );
  }

  const totalUnRead = notifications.filter(
    (item) => item.isUnRead === true
  ).length;

  const renderHead = (
    <Stack
      direction="row"
      alignItems="center"
      sx={{ py: 2, pl: 2.5, pr: 1, minHeight: 68 }}
    >
      <Typography variant="h6" sx={{ flexGrow: 1 }}>
        Notifications
      </Typography>

      <IconButton
        onClick={drawer.onFalse}
        sx={{ display: { xs: 'inline-flex', sm: 'none' } }}
      >
        <Iconify icon="mingcute:close-line" />
      </IconButton>
    </Stack>
  );

  const renderList = (
    <Scrollbar>
      <Box component="ul">
        {notifications?.map((notification) => (
          <Box component="li" key={notification.id} sx={{ display: 'flex' }}>
            <NotificationItem notification={notification} />
          </Box>
        ))}
      </Box>
    </Scrollbar>
  );

  return (
    <>
      <IconButton
        component={m.button}
        whileTap="tap"
        whileHover="hover"
        variants={varHover(1.05)}
        onClick={drawer.onTrue}
        sx={sx}
        {...other}
      >
        <Badge badgeContent={totalUnRead} color="error">
          <SvgIcon>
            {/* https://icon-sets.iconify.design/solar/bell-bing-bold-duotone/ */}
            <path
              fill="currentColor"
              d="M18.75 9v.704c0 .845.24 1.671.692 2.374l1.108 1.723c1.011 1.574.239 3.713-1.52 4.21a25.794 25.794 0 0 1-14.06 0c-1.759-.497-2.531-2.636-1.52-4.21l1.108-1.723a4.393 4.393 0 0 0 .693-2.374V9c0-3.866 3.022-7 6.749-7s6.75 3.134 6.75 7"
              opacity="0.5"
            />
            <path
              fill="currentColor"
              d="M12.75 6a.75.75 0 0 0-1.5 0v4a.75.75 0 0 0 1.5 0zM7.243 18.545a5.002 5.002 0 0 0 9.513 0c-3.145.59-6.367.59-9.513 0"
            />
          </SvgIcon>
        </Badge>
      </IconButton>

      <Drawer
        open={drawer.value}
        onClose={drawer.onFalse}
        anchor="right"
        slotProps={{ backdrop: { invisible: true } }}
        PaperProps={{ sx: { width: 1, maxWidth: 420 } }}
      >
        {renderHead}

        {renderList}
      </Drawer>
    </>
  );
}
