import { SvgColor } from '~/components/svg-color';
import { CONFIG } from '~/config-global';

const icon = (name: string, folderName: string) => (
  <SvgColor
    src={`${CONFIG.assetsDir}/assets/icons/${folderName}/${name}.svg`}
  />
);

export const ICONS = {
  job: icon('ic-job', 'navbar'),
  blog: icon('ic-blog', 'navbar'),
  chat: icon('ic-chat', 'navbar'),
  mail: icon('ic-mail', 'navbar'),
  user: icon('ic-user', 'navbar'),
  file: icon('ic-file', 'navbar'),
  lock: icon('ic-lock', 'navbar'),
  tour: icon('ic-tour', 'navbar'),
  order: icon('ic-order', 'navbar'),
  label: icon('ic-label', 'navbar'),
  blank: icon('ic-blank', 'navbar'),
  kanban: icon('ic-kanban', 'navbar'),
  folder: icon('ic-folder', 'navbar'),
  course: icon('ic-course', 'navbar'),
  banking: icon('ic-banking', 'navbar'),
  booking: icon('ic-booking', 'navbar'),
  invoice: icon('ic-invoice', 'navbar'),
  product: icon('ic-product', 'navbar'),
  calendar: icon('ic-calendar', 'navbar'),
  disabled: icon('ic-disabled', 'navbar'),
  external: icon('ic-external', 'navbar'),
  menuItem: icon('ic-menu-item', 'navbar'),
  ecommerce: icon('ic-ecommerce', 'navbar'),
  analytics: icon('ic-analytics', 'navbar'),
  dashboard: icon('ic-dashboard', 'navbar'),
  parameter: icon('ic-parameter', 'navbar'),
  check: icon('ic-check', 'integrations'),
  alert: icon('ic-warning', 'actions'),
};
