import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { LoadingScreen } from '~/components/loading-screen';
import { DashboardLayout } from '~/layouts/dashboard';

// ----------------------------------------------------------------------

const IndexPage = lazy(() => import('~/pages/dashboard/one'));
const PageTwo = lazy(() => import('~/pages/dashboard/two'));
const PageThree = lazy(() => import('~/pages/dashboard/three'));
const PageFour = lazy(() => import('~/pages/dashboard/four'));
const PageFive = lazy(() => import('~/pages/dashboard/five'));
const PageSix = lazy(() => import('~/pages/dashboard/six'));

// ----------------------------------------------------------------------

const layoutContent = (
  <DashboardLayout>
    <Suspense fallback={<LoadingScreen />}>
      <Outlet />
    </Suspense>
  </DashboardLayout>
);

export const dashboardRoutes = [
  {
    path: 'dashboard',
    element: layoutContent,
    children: [
      { element: <IndexPage />, index: true },
      { path: 'two', element: <PageTwo /> },
      { path: 'three', element: <PageThree /> },
      {
        path: 'group',
        children: [
          { element: <PageFour />, index: true },
          { path: 'five', element: <PageFive /> },
          { path: 'six', element: <PageSix /> },
        ],
      },
    ],
  },
];
