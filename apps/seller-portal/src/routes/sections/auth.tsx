import { lazy } from 'react';

import { AuthSplitLayout } from '~/layouts/auth-split';

import { GuestGuard } from '~/auth/guard';

// ----------------------------------------------------------------------

const SignInPage = lazy(() => import('~/pages/auth/jwt/sign-in'));

// ----------------------------------------------------------------------

export const authRoutes = [
  {
    path: 'sign-in',
    element: (
      <GuestGuard>
        <AuthSplitLayout section={{ title: 'Hi, Welcome back' }}>
          <SignInPage />
        </AuthSplitLayout>
      </GuestGuard>
    ),
  },
];
