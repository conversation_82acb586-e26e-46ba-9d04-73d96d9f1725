// ----------------------------------------------------------------------

const ROOTS = {
  AUTH: '',
  DASHBOARD: '/',
  PRODUCTS: '/products',
  INVENTORY: '/inventory',
  LOCATION: '/locations',
  ORDERS: '/orders',
  USERS: '/users',
  SETTINGS: '/settings',
};

// ----------------------------------------------------------------------

export const paths = {
  home: 'https://www.portless.com',
  faqs: '/faqs',
  // AUTH
  auth: {
    signIn: `${ROOTS.AUTH}/sign-in`,
    signUp: `${ROOTS.AUTH}/sign-up`,
    forgotPassword: `${ROOTS.AUTH}/forgot-password`,
    login: `${ROOTS.AUTH}/login`,
  },
  // DASHBOARD
  dashboard: {
    root: ROOTS.DASHBOARD,
  },
  product: {
    root: ROOTS.PRODUCTS,
    list: ROOTS.PRODUCTS,
    details: (id: string) => `${ROOTS.PRODUCTS}/${id}`,
  },
  inventory: {
    root: ROOTS.INVENTORY,
    list: ROOTS.INVENTORY,
  },
  location: {
    root: ROOTS.LOCATION,
    list: ROOTS.LOCATION,
  },
  order: {
    root: ROOTS.ORDERS,
    new: `${ROOTS.ORDERS}/new`,
    list: ROOTS.ORDERS,
  },
  user: {
    root: ROOTS.USERS,
    new: `${ROOTS.USERS}/new`,
    list: ROOTS.USERS,
  },
  settings: {
    root: ROOTS.SETTINGS,
  },
  support: {
    root: 'https://support.portless.com',
  },
};
