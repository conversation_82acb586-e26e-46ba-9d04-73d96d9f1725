import moment from 'moment';
import { useGetList } from 'react-admin';

import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';

import { LoadingScreen } from '~/components/loading-screen';
import { CONFIG } from '~/config-global';
import { DashboardContent } from '~/layouts/dashboard';

import { AnalyticsShipmentsOverTime } from '../analytics-shipments-over-time';
import { AnalyticsWidgetSummary } from '../analytics-widget-summary';
import { getFeatureFlags, VIEW_ORDER_EXCEPTIONS } from '~/utils/feature-flags';

// ----------------------------------------------------------------------

export default function OverviewAnalyticsView() {
  const { isPending, data = [] } = useGetList('dashboard');

  const isViewOrderExceptionsEnabled =
    getFeatureFlags(VIEW_ORDER_EXCEPTIONS) ?? false;

  const lowStock = data?.find((item) => item.id === 'low_stock');
  const outOfStock = data?.find((item) => item.id === 'out_of_stock');
  const ordersByMonth =
    data?.find((item) => item.id === 'orders_by_month') ?? [];
  const totalOrders = ordersByMonth?.value?.reduce(
    (total, month) => total + month,
    0
  );
  const orderWithExceptions = data?.find(
    (item) => item.id === 'orders_with_exceptions_count'
  );

  const widgetSpacing = isViewOrderExceptionsEnabled ? 4 : 6;

  return (
    <DashboardContent maxWidth="xl">
      <Typography variant="h4" sx={{ mb: { xs: 3, md: 5 } }}>
        Hi, Welcome back{' '}
        <span role="img" aria-label="emoji-wave">
          👋
        </span>
      </Typography>

      <Grid container spacing={3}>
        {isPending ? (
          <LoadingScreen />
        ) : (
          <>
            <Grid item xs={widgetSpacing}>
              <AnalyticsWidgetSummary
                title="Out of stock"
                total={outOfStock?.value}
                color="error"
                icon={
                  <img
                    alt="icon"
                    src={`${CONFIG.assetsDir}/assets/icons/glass/ic-glass-outofstock.svg`}
                  />
                }
              />
            </Grid>

            <Grid item xs={widgetSpacing}>
              <AnalyticsWidgetSummary
                title="Low stock"
                total={lowStock?.value}
                color="warning"
                icon={
                  <img
                    alt="icon"
                    src={`${CONFIG.assetsDir}/assets/icons/glass/ic-glass-lowstock.svg`}
                  />
                }
              />
            </Grid>

            {isViewOrderExceptionsEnabled && (
              <Grid item xs={widgetSpacing}>
                <AnalyticsWidgetSummary
                  title="Exceptions"
                  total={orderWithExceptions?.value}
                  color="error"
                  icon={
                    <img
                      alt="icon"
                      src={`${CONFIG.assetsDir}/assets/icons/glass/ic-glass-buy.svg`}
                    />
                  }
                />
              </Grid>
            )}

            <Grid item xs={12}>
              <AnalyticsShipmentsOverTime
                title="Orders"
                subheader={`${totalOrders} orders`}
                chart={{
                  categories: [
                    'Jan',
                    'Feb',
                    'Mar',
                    'Apr',
                    'May',
                    'Jun',
                    'Jul',
                    'Aug',
                    'Sep',
                    'Oct',
                    'Nov',
                    'Dec',
                  ],
                  series: [
                    {
                      name: moment().year().toString(),
                      data: ordersByMonth?.value ?? [],
                    },
                  ],
                }}
              />
            </Grid>
          </>
        )}
      </Grid>
    </DashboardContent>
  );
}
