import React, { useEffect, useState } from 'react';
import { Card, CardContent, Typography, Box } from '@mui/material';
import { Button } from 'react-admin';
import { createClient } from '@pipedream/sdk/browser';
import { CONFIG } from '~/config-global';
import { ICONS } from '~/layouts/config-icons';
import { getDataProvider } from '~/lib/admin/data-provider';
import { auth, db } from '~/lib/admin/firebase';

//---------------------------------------------------------------------------------------//

const connectAccount = async (
  organizationId,
  integrationId,
  appId,
  appSlug
) => {
  const tokenReponse = await fetch(
    `/api/organization/${organizationId}/integration/${integrationId}/token`,
    { method: 'post' }
  );

  const token = await tokenReponse.json();
  const pd = createClient({
    environment: import.meta.env.MODE,
  });

  pd.connectAccount({
    app: appSlug,
    oauthAppId: appId,
    token: token.token,
    onSuccess: (result) => {
      fetch(`/api/organization/${organizationId}/connection`, {
        method: 'POST',
        body: JSON.stringify({ account_id: result.id }),
        headers: {
          'Content-Type': 'application/json',
        },
      })
        .then((response) => console.log('connected', response))
        .catch((error) => console.error(error));
    },
    onError: (error) => console.error(error),
  });
};

//---------------------------------------------------------------------------------------//

export const ConnectButton = (props) => {
  const { label = 'Enable Integration', record } = props;
  const { id, app_id, app_slug } = record;

  const [organizationId, setOrganizationId] = useState<string | null>(null);

  useEffect(() => {
    const fetchToken = async () => {
      const token = await auth.currentUser?.getIdTokenResult();
      const orgId = token?.claims.organization_id as string;

      if (id && orgId) {
        setOrganizationId(orgId);
      }
    };

    fetchToken();
  }, [id]);

  if (!id || !organizationId) {
    return null;
  }

  return (
    <Button
      label={label}
      size="medium"
      onClick={() => connectAccount(organizationId, id, app_id, app_slug)}
      sx={{
        border: '1px solid',
        borderColor: 'primary.main',
        '&:hover': {
          borderColor: 'primary.dark',
        },
      }}
    />
  );
};

//---------------------------------------------------------------------------------------//

const Connected = () => (
  <Typography
    variant="body2"
    sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      columnGap: '0.5rem',
    }}
  >
    {ICONS.check}
    Connected
  </Typography>
);

//---------------------------------------------------------------------------------------//

interface IntegrationsCardProps {
  id: string;
  app_id: string;
  app_slug: string;
  name: string;
  description: string;
}

//---------------------------------------------------------------------------------------//

export const IntegrationsCard: React.FC<IntegrationsCardProps> = ({
  id,
  app_id,
  app_slug,
  name,
  description,
}) => {
  const [renderConnectButton, setRenderConnectButton] = React.useState(true);

  const checkConnectedStatus = async () => {
    const dataProvider = getDataProvider(auth, db);

    const { data } = await dataProvider.getList('connections', {
      filter: { filterField: 'integration_id', filterValue: id },
    });

    if (data[0]?.account_id) {
      setRenderConnectButton(false);
    }
  };
  useEffect(() => {
    checkConnectedStatus();
  }, []);

  return (
    <Card sx={{ width: 340, backgroundColor: 'white' }}>
      <CardContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            mb: 2,
          }}
        >
          <img
            src={`${CONFIG.assetsDir}/assets/icons/integrations/ic-shopify.svg`}
            alt={name}
          />
        </Box>

        <Typography
          variant="h5"
          component="div"
          sx={{ textAlign: 'center', mb: 1 }}
        >
          {name}
        </Typography>

        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ textAlign: 'center', mb: 2 }}
        >
          {description}
        </Typography>

        <Box sx={{ textAlign: 'center' }}>
          {renderConnectButton ? (
            <ConnectButton record={{ id, app_id, app_slug }} />
          ) : (
            <Connected />
          )}
        </Box>
      </CardContent>
    </Card>
  );
};
