import { use<PERSON><PERSON><PERSON><PERSON>, useGet<PERSON>ist, useListController } from 'react-admin';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import { Location } from '@portless/core';

import { CustomBreadcrumbs } from '~/components/custom-breadcrumbs';
import { Scrollbar } from '~/components/scrollbar';
import {
  emptyRows,
  TableEmptyRows,
  TableNoData,
  TablePaginationCustom,
  TableSkeleton,
} from '~/components/table';
import { DashboardContent } from '~/layouts/dashboard';

import { InventoryTableToolbar } from '../inventory-table-toolbar';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ProductInventoryStatusLabel } from '~/components/status-label/product-inventory-status-label';
import { InventoryTableTabs } from '../inventory-table-tabs';
import { Button } from '@mui/material';
import { exportInventoryData } from '~/utils/csvExport';

const cols = [
  {
    id: 'sku',
    label: 'SKU',
    canSort: true,
    render: (item) => item.sku,
  },
  {
    id: 'name',
    label: 'Product',
    canSort: true,
    render: (item) => <> {item.name}</>,
  },
  {
    id: 'status',
    label: 'Status',
    canSort: true,
    render: (item) => (
      <ProductInventoryStatusLabel status={item['inventory.status']} />
    ),
  },
  {
    id: 'on_hand',
    label: 'On Hand',
    canSort: true,
    render: (item) => item.inventory.on_hand,
  },
  {
    id: 'committed',
    label: 'Committed',
    canSort: true,
    render: (item) => item.inventory.committed,
  },
  {
    id: 'available',
    label: 'Available',
    canSort: true,
    render: (item) => item.inventory.available,
  },
];

const visuallyHidden = {
  border: 0,
  margin: -1,
  padding: 0,
  width: '1px',
  height: '1px',
  overflow: 'hidden',
  position: 'absolute',
  whiteSpace: 'nowrap',
  clip: 'rect(0 0 0 0)',
} as const;

export function InventoryListView() {
  const createPath = useCreatePath();
  const navigate = useNavigate();

  const { data: locations, isPending: isPendingLocation } =
    useGetList<Location>('locations');

  const {
    // data
    data = [],
    total = 0,
    isPending,
    // pagination
    page,
    setPage,
    perPage,
    setPerPage,
    // sort
    sort,
    setSort,
    // filters
    setFilters,
    filterValues,
  } = useListController({
    resource: 'products',
    filterDefaultValues: {
      stock: 'all',
    },
    sort: {
      field: 'sku',
      order: 'ASC',
    },
  });

  useEffect(() => {
    if (locations && locations[0] != null) {
      setFilters({ ...filterValues, location: locations[0].id });
    }
  }, [locations]);

  if (isPendingLocation) {
    return null;
  }

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Inventory"
        links={[]}
        sx={{ mb: { xs: 3, md: 1 } }}
      />

      <Card>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <InventoryTableTabs filters={filterValues} setFilters={setFilters} />

          <Button
            variant="contained"
            size="small"
            color="primary"
            onClick={() => data?.length && exportInventoryData(data)}
            role="tab"
            tabIndex={-1}
            sx={{ mt: 2, mr: 2 }}
          >
            Export
          </Button>
        </Box>
        <InventoryTableToolbar
          filters={filterValues}
          setFilters={setFilters}
          locations={locations}
        />

        <Box sx={{ position: 'relative' }}>
          <Scrollbar>
            <Table size={'medium'} sx={{ minWidth: 960 }}>
              <TableHead>
                <TableRow>
                  {cols.map((col) => (
                    <TableCell
                      key={col.id}
                      sx={
                        (col.id === 'name' ? { width: 390 } : null) ||
                        (col.id === 'sku' ? { width: 190 } : null)
                      }
                      sortDirection={
                        col.canSort && sort.field === col.id
                          ? sort.order === 'DESC'
                            ? 'desc'
                            : 'asc'
                          : false
                      }
                    >
                      <TableSortLabel
                        hideSortIcon
                        active={sort.field === col.id}
                        direction={
                          sort.field === col.id
                            ? sort.order === 'DESC'
                              ? 'desc'
                              : 'asc'
                            : 'asc'
                        }
                        onClick={() =>
                          setSort({
                            field: col.id,
                            order: sort.order === 'ASC' ? 'DESC' : 'ASC',
                          })
                        }
                      >
                        {col.label}
                        {sort.field === col.id ? (
                          <Box sx={{ ...visuallyHidden }}>
                            {sort.order === 'DESC'
                              ? 'sorted descending'
                              : 'sorted ascending'}
                          </Box>
                        ) : null}
                      </TableSortLabel>
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>

              {isPending ? (
                <TableBody>
                  <TableSkeleton />
                </TableBody>
              ) : (
                <TableBody>
                  {data.map((item) => (
                    <TableRow
                      key={item.id}
                      hover
                      tabIndex={-1}
                      style={{ cursor: 'pointer' }}
                      onClick={() =>
                        navigate(
                          createPath({
                            type: 'show',
                            resource: 'products',
                            id: item.id,
                          })
                        )
                      }
                    >
                      {cols.map((col) => (
                        <TableCell key={col.id}>{col.render(item)}</TableCell>
                      ))}
                    </TableRow>
                  ))}

                  <TableEmptyRows
                    height={56}
                    emptyRows={emptyRows(page - 1, perPage, total)}
                  />

                  <TableNoData notFound={total === 0} />
                </TableBody>
              )}
            </Table>
          </Scrollbar>
        </Box>

        <TablePaginationCustom
          page={page - 1}
          dense={true}
          count={total}
          rowsPerPage={perPage}
          onPageChange={(_event, number) => setPage(number + 1)}
          onRowsPerPageChange={(event) =>
            setPerPage(Number(event.target.value))
          }
        />
      </Card>
    </DashboardContent>
  );
}
