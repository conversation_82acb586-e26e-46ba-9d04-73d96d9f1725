import { ChangeEvent, useCallback } from 'react';

import Checkbox from '@mui/material/Checkbox';
import FormControl from '@mui/material/FormControl';
import InputAdornment from '@mui/material/InputAdornment';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import OutlinedInput from '@mui/material/OutlinedInput';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';

import { Iconify } from '~/components/iconify';
import { IInventoryTableFilters } from '~/types/inventory';

type Props = {
  filters: IInventoryTableFilters;
  setFilters: (filters: IInventoryTableFilters) => void;
  locations: { id: string; name: string }[] | undefined;
};

export function InventoryTableToolbar({
  filters,
  setFilters,
  locations,
}: Props) {
  const handleQuery = useCallback(
    (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setFilters({ ...filters, q: event.target.value });
    },
    [filters, setFilters]
  );

  const handleLocation = useCallback(
    (event: SelectChangeEvent) => {
      setFilters({ ...filters, location: event.target.value });
    },
    [filters, setFilters]
  );

  if (locations == null) {
    return null;
  }

  return (
    <Stack
      spacing={2}
      alignItems={{ xs: 'flex-end', md: 'center' }}
      sx={{ p: 2.5, pr: { xs: 2.5, md: 1 } }}
    >
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        flexGrow={1}
        sx={{ width: 1 }}
      >
        <FormControl
          sx={{
            flexShrink: 0,
            width: { xs: 1, md: 180 },
          }}
        >
          <InputLabel>Location</InputLabel>

          <Select
            value={filters.location}
            onChange={handleLocation}
            input={<OutlinedInput label="Location" />}
            renderValue={(selected) =>
              locations.find((location) => location.id === selected)?.name
            }
            sx={{ textTransform: 'capitalize' }}
          >
            {locations.map((location) => (
              <MenuItem key={location.id} value={location.id}>
                <Checkbox
                  disableRipple
                  size="small"
                  checked={filters.location === location.id}
                />
                {location.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <TextField
          sx={{ flex: 1 }}
          onChange={handleQuery}
          placeholder="Search by SKU, name..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify
                  icon="eva:search-fill"
                  sx={{ color: 'text.disabled' }}
                />
              </InputAdornment>
            ),
          }}
        />
      </Stack>
    </Stack>
  );
}
