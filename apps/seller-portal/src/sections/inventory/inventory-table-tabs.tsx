import { Label } from '~/components/label';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';

import { varAlpha } from '~/theme/styles';
import { useListController } from 'react-admin';

const STOCK_OPTIONS = [
  { value: 'all', label: 'All' },
  { value: 'in_stock', label: 'In Stock' },
  { value: 'low_stock', label: 'Low Stock' },
  { value: 'out_of_stock', label: 'Out Of Stock' },
];

type Props = {
  filters: any;
  setFilters: (filters: any) => void;
};

export function InventoryTableTabs({ filters, setFilters }: Props) {
  const { stock, ...rest } = filters; // preserve other filters
  const { total, meta } = useListController({
    resource: 'products',
    disableSyncWithLocation: true, // don't use path params in controller
    filter: rest,
  });

  const counts = {};
  STOCK_OPTIONS.forEach((option) => {
    const count =
      option.value === 'all'
        ? total
        : meta?.statuses?.find((status) => status.value === option.value)
            ?.count ?? 0;
    counts[option.value] = count;
  });

  return (
    <Tabs
      value={filters.stock}
      onChange={(_event, value) => setFilters({ ...filters, stock: value })}
      sx={{
        px: 2.5,
        boxShadow: (theme) =>
          `inset 0 -2px 0 0 ${varAlpha(
            theme.vars.palette.grey['500Channel'],
            0.08
          )}`,
      }}
    >
      {STOCK_OPTIONS.map((tab) => (
        <Tab
          key={tab.value}
          iconPosition="end"
          value={tab.value}
          label={tab.label}
          icon={
            <Label
              variant={
                ((tab.value === 'all' || tab.value === filters.stock) &&
                  'filled') ||
                'soft'
              }
              color={
                (tab.value === 'in_stock' && 'success') ||
                (tab.value === 'low_stock' && 'warning') ||
                (tab.value === 'out_of_stock' && 'error') ||
                'default'
              }
            >
              {counts[tab.value]}
            </Label>
          }
        />
      ))}
    </Tabs>
  );
}
