import Chip from '@mui/material/Chip';
import type { SxProps, Theme } from '@mui/material/styles';

import {
  chipProps,
  FiltersBlock,
  FiltersResult,
} from '~/components/filters-result';
import { IInventoryTableFilters } from '~/types/inventory';

// ----------------------------------------------------------------------

type Props = {
  totalResults: number;
  sx?: SxProps<Theme>;
  filters: IInventoryTableFilters;
  setFilters: (filters: IInventoryTableFilters) => void;
};

export function InventoryTableFiltersResult({
  filters,
  setFilters,
  totalResults,
  sx,
}: Props) {
  return (
    <FiltersResult
      totalResults={totalResults}
      onReset={() =>
        setFilters({ ...filters, name: '', sku: '', stock: 'all' })
      }
      sx={sx}
    >
      <FiltersBlock label="SKU:" isShow={!!filters.sku}>
        <Chip
          {...chipProps}
          label={filters.sku}
          onDelete={() => setFilters({ ...filters, sku: '' })}
        />
      </FiltersBlock>

      <FiltersBlock label="Name:" isShow={!!filters.name}>
        <Chip
          {...chipProps}
          label={filters.name}
          onDelete={() => setFilters({ ...filters, name: '' })}
        />
      </FiltersBlock>
    </FiltersResult>
  );
}
