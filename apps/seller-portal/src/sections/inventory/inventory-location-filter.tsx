import type { ButtonBaseProps } from '@mui/material/ButtonBase';

import { useState, useCallback } from 'react';

import Box from '@mui/material/Box';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';

import { usePopover } from '~/components/custom-popover';

// ----------------------------------------------------------------------

const locations = [
  { id: 'abcd1234', name: 'ABCD1234' },
  { id: 'abcd7890', name: 'WXYZ' },
];

export type LocationsPopoverProps = ButtonBaseProps & {
  onSelectLocation?: (location: string) => void;
};

export function LocationsPopover({ sx, ...other }: LocationsPopoverProps) {
  const popover = usePopover();
  const [location, setLocation] = useState();

  const handleClickListItem = useCallback(
    (location) => {
      setLocation(location);
      popover.onClose();
    },
    [popover]
  );

  const handleMenuItemClick = useCallback(
    (event: React.MouseEvent<HTMLElement>, index: number) => {
      setSelectedIndex(index);
      setOpenList(null);
    },
    []
  );

  return (
    <Box>
      <List component="nav" aria-label="Device settings">
        <ListItemButton
          aria-haspopup="true"
          aria-controls="lock-menu"
          aria-label="when device is locked"
          onClick={handleClickListItem}
          disableRipple
        >
          <ListItemText primary={locations[selectedIndex]?.name} />
        </ListItemButton>
      </List>
      <Menu
        id="lock-menu"
        anchorEl={isOpenList}
        onClose={onClose}
        open={Boolean(isOpenList)}
        onBlur={onClose}
      >
        {locations.map((option, index) => (
          <MenuItem
            key={index}
            selected={index === selectedIndex}
            onClick={(event) => handleMenuItemClick(event, index)}
          >
            {locations[index]?.name}
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
}
