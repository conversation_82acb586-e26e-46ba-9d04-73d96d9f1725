import Chip from '@mui/material/Chip';
import type { SxProps, Theme } from '@mui/material/styles';
import { isEmpty } from 'lodash';
import { useCallback } from 'react';

import {
  chipProps,
  FiltersBlock,
  FiltersResult,
} from '~/components/filters-result';
import type { IProductTableFilters } from '~/types/product';

// ----------------------------------------------------------------------

type Props = {
  totalResults: number;
  sx?: SxProps<Theme>;
  filters: IProductTableFilters;
  setFilters: (filters: IProductTableFilters) => void;
};

export function OrderTableFiltersResult({
  filters,
  setFilters,
  totalResults,
  sx,
}: Props) {
  const handleRemoveQuery = useCallback(() => {
    const { q, ...rest } = filters;
    setFilters(rest);
  }, [filters, setFilters]);

  return (
    <FiltersResult
      totalResults={totalResults}
      onReset={() => handleRemoveQuery()}
      sx={sx}
    >
      <FiltersBlock label="Search:" isShow={!isEmpty(filters.q)}>
        <Chip
          {...chipProps}
          key={`filters-q}`}
          label={filters.q}
          onDelete={() => handleRemoveQuery()}
        />
      </FiltersBlock>
    </FiltersResult>
  );
}
