import InputAdornment from '@mui/material/InputAdornment';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import { ChangeEvent, useCallback } from 'react';

import { Iconify } from '~/components/iconify';
import { IOrderTableFilters } from '~/types/order';

type Props = {
  filters: IOrderTableFilters;
  setFilters: (filter: IOrderTableFilters) => void;
};

export function OrderTableToolbar({ filters, setFilters }: Props) {
  const handleQuery = useCallback(
    (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setFilters({ ...filters, q: event.target.value });
    },
    [filters, setFilters]
  );

  return (
    <Stack
      spacing={2}
      alignItems={{ xs: 'flex-end', md: 'center' }}
      direction={{ xs: 'column', md: 'row' }}
      sx={{ p: 2.5, pr: { xs: 2.5, md: 1 } }}
    >
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        flexGrow={1}
        sx={{ width: 1 }}
      >
        <TextField
          sx={{ flex: 1 }}
          defaultValue={filters.q}
          onChange={handleQuery}
          placeholder="Search by order ID, customer..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify
                  icon="eva:search-fill"
                  sx={{ color: 'text.disabled' }}
                />
              </InputAdornment>
            ),
          }}
        />
      </Stack>
    </Stack>
  );
}
