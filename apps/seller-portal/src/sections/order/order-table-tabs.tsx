import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { useListController } from 'react-admin';

import { Label } from '~/components/label';
import { varAlpha } from '~/theme/styles';
import { VIEW_ORDER_EXCEPTIONS, getFeatureFlags } from '~/utils/feature-flags';

const STATUS_OPTIONS = [
  { value: 'all', label: 'All' },
  { value: '1', label: 'Pending' },
  { value: '2', label: 'In Progress' },
  { value: '3', label: 'Shipped' },
  { value: '4', label: 'Delivered' },
  { value: '5', label: 'Cancelled' },
];

type Props = {
  filters: any;
  setFilters: (filterValues: any) => void;
};

export function OrderTableTabs({ filters, setFilters }: Props) {
  const { status, ...rest } = filters;
  const { total, meta } = useListController({
    disableSyncWithLocation: true,
    filter: rest,
  });

  const isViewOrderExceptionsEnabled =
    getFeatureFlags(VIEW_ORDER_EXCEPTIONS) ?? false;

  const hasExceptionsInStatusOptions = STATUS_OPTIONS.some(
    (option) => option.value === '0'
  );

  if (isViewOrderExceptionsEnabled && !hasExceptionsInStatusOptions) {
    STATUS_OPTIONS.push({ value: '0', label: 'Exception' });
  }

  const counts = {};
  STATUS_OPTIONS.forEach((option) => {
    let count = 0;

    if (option.value === '0') {
      // exceptions
      count = meta?.has_exceptions?.[0]?.count ?? 0;
    } else {
      count =
        option.value === 'all'
          ? total
          : meta?.statuses?.find((status) => status.value === option.value)
              ?.count ?? 0;
    }

    counts[option.value] = count;
  });

  return (
    <Tabs
      value={filters.status ?? 'all'}
      onChange={(_event, value) => setFilters({ ...filters, status: value })}
      sx={{
        px: 2.5,
        boxShadow: (theme) =>
          `inset 0 -2px 0 0 ${varAlpha(
            theme.vars.palette.grey['500Channel'],
            0.08
          )}`,
      }}
    >
      {STATUS_OPTIONS.map((tab) => (
        <Tab
          key={tab.value}
          iconPosition="end"
          value={tab.value}
          label={tab.label}
          icon={
            <Label
              variant={
                ((tab.value === 'all' || tab.value === filters.status) &&
                  'filled') ||
                'soft'
              }
              color={
                (tab.value === '1' && 'default') ||
                (tab.value === '2' && 'info') ||
                (tab.value === '3' && 'success') ||
                (tab.value === '5' && 'error') ||
                'default'
              }
            >
              {counts[tab.value]}
            </Label>
          }
        />
      ))}
    </Tabs>
  );
}
