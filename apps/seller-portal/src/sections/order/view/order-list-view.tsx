import { use<PERSON>reate<PERSON><PERSON>, useList<PERSON>ontroller } from 'react-admin';
import { useNavigate } from 'react-router-dom';

import Box from '@mui/material/Box';
import { Button } from '@mui/material';
import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';

import { getCountryByCode } from '~/assets/data';
import { CustomBreadcrumbs } from '~/components/custom-breadcrumbs';
import { Scrollbar } from '~/components/scrollbar';
import { OrderStatusLabel } from '~/components/status-label/order-status-label';
import {
  emptyRows,
  TableEmptyRows,
  TableNoData,
  TablePaginationCustom,
  TableSkeleton,
} from '~/components/table';
import { DashboardContent } from '~/layouts/dashboard';
import { fDate } from '~/utils/format-time';
import { VIEW_ORDER_EXCEPTIONS, getFeatureFlags } from '~/utils/feature-flags';
import { exportOrderData } from '~/utils/csvExport';
import { getExceptionsText } from '~/utils/exceptions-utils';

import { OrderTableTabs } from '../order-table-tabs';
import { OrderTableToolbar } from '../order-table-toolbar';

const cols = [
  {
    id: 'order_id',
    label: 'Order',
    canSort: true,
    render: (item) => item.ext_id,
  },
  {
    id: 'created_at',
    label: 'Date',
    canSort: true,
    render: (item) => fDate(item.created_at),
  },
  {
    id: 'items',
    label: 'Items',
    canSort: true,
    render: (item) => item.items.length,
  },
  {
    id: 'customer',
    label: 'Customer',
    canSort: true,
    render: (item) => item.customer.name,
  },
  {
    id: 'destination',
    label: 'Destination',
    canSort: true,
    render: (item) => getCountryByCode(item.destination.country_code)?.label,
  },
  {
    id: 'status',
    label: 'Status',
    canSort: true,
    render: (item) => (
      <OrderStatusLabel
        status={String(item.status)}
        exception={Boolean(item.has_exceptions)}
      />
    ),
  },
];

const visuallyHidden = {
  border: 0,
  margin: -1,
  padding: 0,
  width: '1px',
  height: '1px',
  overflow: 'hidden',
  position: 'absolute',
  whiteSpace: 'nowrap',
  clip: 'rect(0 0 0 0)',
} as const;

export function OrderListView() {
  const createPath = useCreatePath();
  const navigate = useNavigate();

  const isViewOrderExceptionsEnabled =
    getFeatureFlags(VIEW_ORDER_EXCEPTIONS) ?? false;

  const hasExceptionsInCols = cols.some((col) => col.id === 'exceptions');

  if (isViewOrderExceptionsEnabled && !hasExceptionsInCols) {
    cols.push({
      id: 'exceptions',
      label: 'Exceptions',
      canSort: false,
      render: (item) => getExceptionsText(item.exceptions),
    });
  }

  const {
    // data
    data = [],
    total = 0,
    isPending,
    // pagination
    page,
    setPage,
    perPage,
    setPerPage,
    // sort
    sort,
    setSort,
    // filters
    setFilters,
    filterValues,
  } = useListController({
    filterDefaultValues: {
      status: 'all',
    },
    sort: {
      field: 'created_at',
      order: 'DESC',
    },
  });

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Orders"
        links={[]}
        sx={{ mb: { xs: 3, md: 1 } }}
      />

      <Card>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <OrderTableTabs filters={filterValues} setFilters={setFilters} />
          <Button
            variant="contained"
            size="small"
            color="primary"
            onClick={() => data?.length && exportOrderData(data)}
            role="tab"
            tabIndex={-1}
            sx={{ mt: 2, mr: 2 }}
          >
            Export
          </Button>
        </Box>
        <OrderTableToolbar filters={filterValues} setFilters={setFilters} />
        <Box sx={{ position: 'relative' }}>
          <Scrollbar>
            <Table size={'medium'} sx={{ minWidth: 960 }}>
              <TableHead>
                <TableRow>
                  {cols.map((col) => (
                    <TableCell
                      key={col.id}
                      sx={
                        (col.id === 'name' ? { width: 640 } : null) ||
                        (col.id === 'sku' ? { width: 190 } : null)
                      }
                      sortDirection={
                        col.canSort && sort.field === col.id
                          ? sort.order === 'DESC'
                            ? 'desc'
                            : 'asc'
                          : false
                      }
                    >
                      <TableSortLabel
                        hideSortIcon
                        active={sort.field === col.id}
                        direction={
                          sort.field === col.id
                            ? sort.order === 'DESC'
                              ? 'desc'
                              : 'asc'
                            : 'asc'
                        }
                        onClick={() =>
                          setSort({
                            field: col.id,
                            order: sort.order === 'ASC' ? 'DESC' : 'ASC',
                          })
                        }
                      >
                        {col.label}
                        {sort.field === col.id ? (
                          <Box sx={{ ...visuallyHidden }}>
                            {sort.order === 'DESC'
                              ? 'sorted descending'
                              : 'sorted ascending'}
                          </Box>
                        ) : null}
                      </TableSortLabel>
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>

              {isPending ? (
                <TableBody>
                  <TableSkeleton />
                </TableBody>
              ) : (
                <TableBody>
                  {data.map((item) => (
                    <TableRow
                      key={item.id}
                      hover
                      tabIndex={-1}
                      style={{ cursor: 'pointer' }}
                      onClick={() =>
                        navigate(
                          createPath({
                            type: 'show',
                            resource: 'orders',
                            id: item.id,
                          })
                        )
                      }
                    >
                      {cols.map((col) => (
                        <TableCell key={col.id}>{col.render(item)}</TableCell>
                      ))}
                    </TableRow>
                  ))}

                  <TableEmptyRows
                    height={56}
                    emptyRows={emptyRows(page - 1, perPage, total)}
                  />

                  <TableNoData notFound={total === 0} />
                </TableBody>
              )}
            </Table>
          </Scrollbar>
        </Box>

        <TablePaginationCustom
          page={page - 1}
          dense={true}
          count={total}
          rowsPerPage={perPage}
          onPageChange={(_event, number) => setPage(number + 1)}
          onRowsPerPageChange={(event) =>
            setPerPage(Number(event.target.value))
          }
        />
      </Card>
    </DashboardContent>
  );
}
