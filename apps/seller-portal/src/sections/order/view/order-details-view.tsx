import { useShowController } from 'react-admin';

import Container from '@mui/material/Container';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import Grid from '@mui/material/Unstable_Grid2';
import { Order } from '@portless/core';

import { paths } from '~/routes/paths';
import { LoadingScreen } from '~/components/loading-screen';
import { View500 } from '~/sections/error';
import { getExceptionsText } from '~/utils/exceptions-utils';
import { VIEW_ORDER_EXCEPTIONS, getFeatureFlags } from '~/utils/feature-flags';

import OrderDetailsEstimatedDelivery from '../order-details-estimated-delivery';
import OrderDetailsHistory from '../order-details-history';
import OrderDetailsInfo from '../order-details-info';
import OrderDetailsItems from '../order-details-item';
import OrderDetailsMilestones from '../order-details-milestones';
import OrderDetailsToolbar from '../order-details-toolbar';
import {
  exceptionLabels,
  stuckOrderStatus,
} from '../order-details-tracking-labels';

export function OrderDetailsView() {
  const {
    record: order,
    isPending,
    error,
    defaultTitle,
  } = useShowController<Order & { id: string }>();

  if (isPending) {
    return (
      <Container>
        <LoadingScreen title={defaultTitle} />
      </Container>
    );
  }

  if (error) {
    return <View500 />;
  }

  // get the last fulfillment
  const exceptions = getExceptionsText(
    order.fulfillments
      .filter((f) => Array.isArray(f.exceptions))
      .map((f) => f.exceptions)
      .flat()
  );
  const hasExceptions = exceptions.length > 0;
  const fulfillment = order.fulfillments?.at(order.fulfillments?.length - 1);
  const shipment = fulfillment?.shipment;
  const status = hasExceptions ? '0' : order.status;
  const isDelivered = status === '4';
  const isViewOrderExceptionsEnabled =
    getFeatureFlags(VIEW_ORDER_EXCEPTIONS) ?? false;

  // Checking if has shippedAt to avoid "Invalid time value - Invalid time value"
  const shouldShowEstimatedDelivery =
    (!!shipment?.shippedAt || isDelivered || hasExceptions) &&
    isViewOrderExceptionsEnabled;

  return (
    <Container>
      <OrderDetailsToolbar
        backLink={paths.order.root}
        orderNumber={order.id}
        intOrderNumber={order.int_id}
        extOrderNumber={order.ext_id}
        status={status}
        syncLogistics={order.sync_logistics ? order.sync_logistics : '-1'}
        hasExceptions={hasExceptions}
      />

      <Grid container spacing={3}>
        <Grid xs={12} md={8}>
          <Stack spacing={3} direction={{ xs: 'column-reverse', md: 'column' }}>
            {order.sync_logistics &&
              stuckOrderStatus.includes(order.sync_logistics) && (
                <Alert severity="error">
                  {exceptionLabels[String(order.sync_logistics)]}
                </Alert>
              )}
            {hasExceptions && isViewOrderExceptionsEnabled && (
              <Alert severity="error">{exceptions}</Alert>
            )}
            <OrderDetailsMilestones
              status={order.status !== '5' ? Number(order.status) : -1} // if status is 5, i.e., cancelled, we display grey milestones bar
            />
            <OrderDetailsItems items={order.items} />
            <OrderDetailsHistory fulfillment={order.fulfillments?.[0]} />
          </Stack>
        </Grid>

        <Grid xs={12} md={4}>
          <Stack spacing={3} direction={{ xs: 'column-reverse', md: 'column' }}>
            {shouldShowEstimatedDelivery && (
              <OrderDetailsEstimatedDelivery
                shipment={shipment}
                hasExceptions={hasExceptions}
                exceptions={exceptions}
                isDelivered={isDelivered}
              />
            )}
            <OrderDetailsInfo
              customer={order.customer}
              shipment={shipment}
              destination={order.destination}
              createdAt={order.created_at}
            />
          </Stack>
        </Grid>
      </Grid>
    </Container>
  );
}
