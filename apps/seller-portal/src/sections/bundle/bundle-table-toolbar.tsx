import InputAdornment from '@mui/material/InputAdornment';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import { ChangeEvent, useCallback } from 'react';

import { Iconify } from '~/components/iconify';
import { IProductTableFilters } from '~/types/product';

type Props = {
  filters: IProductTableFilters;
  setFilters: (filter: IProductTableFilters) => void;
};

export function BundleTableToolbar({ filters, setFilters }: Props) {
  const handleSku = useCallback(
    (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setFilters({ ...filters, sku: event.target.value });
    },
    [filters, setFilters]
  );

  const handleName = useCallback(
    (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setFilters({ ...filters, name: event.target.value });
    },
    [filters, setFilters]
  );

  return (
    <Stack
      spacing={2}
      alignItems={{ xs: 'flex-end', md: 'center' }}
      direction={{ xs: 'column', md: 'row' }}
      sx={{ p: 2.5, pr: { xs: 2.5, md: 1 } }}
    >
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        flexGrow={1}
        sx={{ width: 1 }}
      >
        <TextField
          sx={{ flex: 1 }}
          defaultValue={filters.sku}
          onChange={handleSku}
          placeholder="Search by SKU..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify
                  icon="eva:search-fill"
                  sx={{ color: 'text.disabled' }}
                />
              </InputAdornment>
            ),
          }}
        />

        <TextField
          sx={{ flex: 2 }}
          defaultValue={filters.name}
          onChange={handleName}
          placeholder="Search by bundle..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify
                  icon="eva:search-fill"
                  sx={{ color: 'text.disabled' }}
                />
              </InputAdornment>
            ),
          }}
        />
      </Stack>
    </Stack>
  );
}
