import ListItemText from '@mui/material/ListItemText';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { useGetMany } from 'react-admin';

import { Inventory, Location } from '@portless/models/types';

import { ProductStatusLabel } from '~/components/status-label';
import { TableNoData, TableSkeleton } from '~/components/table';

type InventoryLocation = Inventory & Location;

const cols = [
  {
    id: 'location',
    label: 'Location',
    render: (item: InventoryLocation) => {
      const { name, address } = item;
      return (
        <ListItemText
          primary={name}
          secondary={`${address.street}, ${address.city}, ${address.state}, ${address.country} ${address.postalCode}`}
        />
      );
    },
  },
  {
    id: 'status',
    label: 'Status',
    render: (item: InventoryLocation) => (
      <ProductStatusLabel quantity={item.available} />
    ),
  },
  {
    id: 'on_hand',
    label: 'On Hand',
    render: (item: InventoryLocation) => item.on_hand ?? 'N/A',
  },
  {
    id: 'committed',
    label: 'Committed',
    render: (item: InventoryLocation) => item.committed ?? 'N/A',
  },
  {
    id: 'available',
    label: 'Available',
    render: (item: InventoryLocation) => item.available ?? 'N/A',
  },
];

const visuallyHidden = {
  border: 0,
  margin: -1,
  padding: 0,
  width: '1px',
  height: '1px',
  overflow: 'hidden',
  position: 'absolute',
  whiteSpace: 'nowrap',
  clip: 'rect(0 0 0 0)',
} as const;

type Props = {
  inventory: Inventory[];
};

export function BundleInventoryTable({ inventory }: Props) {
  const { data: locations = [], isPending } = useGetMany<
    Location & { id: string }
  >('locations', {
    ids: inventory.map((i) => i.location_id),
  });

  const data = inventory.map((inventoryItem) => {
    const location = locations.find(
      (loc) => loc.id === inventoryItem.location_id
    );

    return {
      ...inventoryItem,
      ...location,
      name: location?.name ?? 'Unknown',
      address: location?.address ?? {
        city: '',
        country: '',
        postalCode: '',
        state: '',
        street: '',
      },
    };
  });

  const notFound = data.length === 0;

  return (
    <Table size={'small'} sx={{ minWidth: 960 }}>
      <TableHead>
        <TableRow>
          {cols.map((col) => (
            <TableCell
              key={col.id}
              sx={col.id === 'available' ? { width: 2 } : null}
            >
              {col.label}
            </TableCell>
          ))}
        </TableRow>
      </TableHead>

      <TableBody>
        {isPending ? (
          <TableSkeleton />
        ) : (
          data.map((location) => (
            <TableRow key={location.id} hover tabIndex={-1}>
              {cols.map((col) => (
                <TableCell key={location.id}>{col.render(location)}</TableCell>
              ))}
            </TableRow>
          ))
        )}
        <TableNoData notFound={notFound} />
      </TableBody>
    </Table>
  );
}
