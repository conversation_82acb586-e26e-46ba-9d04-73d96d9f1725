import { isEmpty } from 'lodash';
import {
  CreatePath<PERSON>ara<PERSON>,
  Link,
  useCreate<PERSON><PERSON>,
  useListController,
} from 'react-admin';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Tab from '@mui/material/Tab';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import Tabs from '@mui/material/Tabs';
import { Product } from '@portless/core';

import { CustomBreadcrumbs } from '~/components/custom-breadcrumbs';
import { Label } from '~/components/label';
import { Scrollbar } from '~/components/scrollbar';
import { ProductStatusLabel } from '~/components/status-label';
import {
  emptyRows,
  TableEmptyRows,
  TableNoData,
  TablePaginationCustom,
  TableSkeleton,
} from '~/components/table';
import { DashboardContent } from '~/layouts/dashboard';
import { varAlpha } from '~/theme/styles';
import { IProductInventoryItem } from '~/types/product';
import { useGetSettings } from '~/utils/settings';

import { BundleTableFiltersResult } from '../bundle-table-filters-result';
import { BundleTableToolbar } from '../bundle-table-toolbar';

const STOCK_OPTIONS = [
  { value: 'all', label: 'All' },
  { value: 'in_stock', label: 'In Stock' },
  { value: 'low_stock', label: 'Low Stock' },
  { value: 'out_of_stock', label: 'Out Of Stock' },
];

const stockStatusCount = (
  status: string,
  items: IProductInventoryItem[],
  low_stock_threshold: number
) => {
  if (status === 'all') {
    return items.length;
  }

  if (status === 'in_stock') {
    return items.filter(
      (item) => item.inventory.available > low_stock_threshold
    ).length;
  }

  if (status === 'low_stock') {
    return items.filter(
      (item) =>
        item.inventory.available > 0 &&
        item.inventory.available <= low_stock_threshold
    ).length;
  }

  if (status === 'out_of_stock') {
    return items.filter((item) => item.inventory.available <= 0).length;
  }

  return 0;
};

const cols = [
  {
    id: 'sku',
    label: 'SKU',
    canSort: true,
    render: (item: IProductInventoryItem) => item.sku,
  },
  {
    id: 'name',
    label: 'Bundle',
    canSort: true,
    render: (
      item: IProductInventoryItem,
      createPath: (params: CreatePathParams) => string
    ) => (
      <Link
        to={createPath({
          type: 'show',
          resource: 'bundles',
          id: item.id,
        })}
        color="inherit"
        variant="subtitle2"
        sx={{ cursor: 'pointer' }}
      >
        {item.name}
      </Link>
    ),
  },
  {
    id: 'status',
    label: 'Status',
    canSort: true,
    render: (item: IProductInventoryItem) => (
      <ProductStatusLabel quantity={item.inventory.available} />
    ),
  },
  {
    id: 'available',
    label: 'Stock',
    canSort: true,
    render: (item: IProductInventoryItem) => item.inventory.available,
  },
];

const visuallyHidden = {
  border: 0,
  margin: -1,
  padding: 0,
  width: '1px',
  height: '1px',
  overflow: 'hidden',
  position: 'absolute',
  whiteSpace: 'nowrap',
  clip: 'rect(0 0 0 0)',
} as const;

export function BundleListView() {
  const createPath = useCreatePath();
  const {
    // data
    data = [],
    total = 0,
    isPending,
    // pagination
    page,
    setPage,
    perPage,
    setPerPage,
    // sort
    sort,
    setSort,
    // filters
    setFilters,
    filterValues,
  } = useListController<Product & { id: string }>({
    filterDefaultValues: {
      stock: 'all',
    },
  });
  const {
    products: { low_inventory_threshold },
  } = useGetSettings();

  const results = data.map((product) => {
    const inventory = (product as Product).inventory?.length
      ? (product as Product).inventory
      : [
          {
            location_id: '',
            available: 0,
            committed: 0,
            incoming: 0,
            on_hand: 0,
            reserved: 0,
          },
        ];

    return {
      ...product,
      inventory: inventory.reduce((total, current) => ({
        location_id: product.id, // not used
        available: total.available + current.available,
        committed: total.committed + current.committed,
        on_hand: total.on_hand + current.on_hand,
      })),
    } as IProductInventoryItem;
  });

  let filteredResults = results;

  if (filterValues.sku) {
    filteredResults = filteredResults.filter((item) =>
      item.sku.toLowerCase().trim().includes(filterValues.sku.trim())
    );
  }

  if (filterValues.name) {
    filteredResults = filteredResults.filter((item) =>
      item.name.toLowerCase().trim().includes(filterValues.name.trim())
    );
  }

  const totalFilteredResults = filteredResults.filter((item) => {
    switch (filterValues.stock) {
      case 'all':
        return true;
      case 'low_stock':
        return (
          item.inventory.available > 0 &&
          item.inventory.available <= low_inventory_threshold
        );
      case 'in_stock':
        return (
          item.inventory.available > 0 &&
          item.inventory.available > low_inventory_threshold
        );
      case 'out_of_stock':
        return item.inventory.available <= 0;
      default:
        return false;
    }
  });

  const paginatedResults = totalFilteredResults.slice(
    (page - 1) * perPage,
    page * perPage
  );

  const canReset = !isEmpty(filterValues.name) || !isEmpty(filterValues.sku);

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Bundles"
        links={[]}
        sx={{ mb: { xs: 3, md: 1 } }}
      />

      <Card>
        <Tabs
          value={filterValues.stock}
          onChange={(_event, value) => setFilters({ stock: value })}
          sx={{
            px: 2.5,
            boxShadow: (theme) =>
              `inset 0 -2px 0 0 ${varAlpha(
                theme.vars.palette.grey['500Channel'],
                0.08
              )}`,
          }}
        >
          {STOCK_OPTIONS.map((tab) => (
            <Tab
              key={tab.value}
              iconPosition="end"
              value={tab.value}
              label={tab.label}
              icon={
                <Label
                  variant={
                    ((tab.value === 'all' ||
                      tab.value === filterValues.stock) &&
                      'filled') ||
                    'soft'
                  }
                  color={
                    (tab.value === 'in_stock' && 'success') ||
                    (tab.value === 'low_stock' && 'warning') ||
                    (tab.value === 'out_of_stock' && 'error') ||
                    'default'
                  }
                >
                  {stockStatusCount(
                    tab.value,
                    filteredResults,
                    low_inventory_threshold
                  )}
                </Label>
              }
            />
          ))}
        </Tabs>

        <BundleTableToolbar filters={filterValues} setFilters={setFilters} />

        {canReset && (
          <BundleTableFiltersResult
            totalResults={total}
            filters={filterValues}
            setFilters={setFilters}
            sx={{ p: 2.5, pt: 0 }}
          />
        )}

        <Box sx={{ position: 'relative' }}>
          <Scrollbar>
            <Table size={'medium'} sx={{ minWidth: 960 }}>
              <TableHead>
                <TableRow>
                  {cols.map((col) => (
                    <TableCell
                      sx={
                        (col.id === 'name' ? { width: 640 } : null) ||
                        (col.id === 'sku' ? { width: 190 } : null)
                      }
                      sortDirection={
                        col.canSort && sort.field === col.id
                          ? sort.order === 'DESC'
                            ? 'desc'
                            : 'asc'
                          : false
                      }
                    >
                      <TableSortLabel
                        hideSortIcon
                        active={sort.field === col.id}
                        direction={
                          sort.field === col.id
                            ? sort.order === 'DESC'
                              ? 'desc'
                              : 'asc'
                            : 'asc'
                        }
                        onClick={() =>
                          setSort({
                            field: col.id,
                            order: sort.order === 'ASC' ? 'DESC' : 'ASC',
                          })
                        }
                      >
                        {col.label}
                        {sort.field === col.id ? (
                          <Box sx={{ ...visuallyHidden }}>
                            {sort.order === 'DESC'
                              ? 'sorted descending'
                              : 'sorted ascending'}
                          </Box>
                        ) : null}
                      </TableSortLabel>
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>

              {isPending ? (
                <TableBody>
                  <TableSkeleton />
                </TableBody>
              ) : (
                <TableBody>
                  {paginatedResults.map((item) => (
                    <TableRow key={item.id} hover tabIndex={-1}>
                      {cols.map((col) => (
                        <TableCell>{col.render(item, createPath)}</TableCell>
                      ))}
                    </TableRow>
                  ))}

                  <TableEmptyRows
                    height={56}
                    emptyRows={emptyRows(page - 1, perPage, total)}
                  />

                  <TableNoData notFound={total === 0} />
                </TableBody>
              )}
            </Table>
          </Scrollbar>
        </Box>

        <TablePaginationCustom
          page={page - 1}
          dense={true}
          count={totalFilteredResults.length}
          rowsPerPage={perPage}
          onPageChange={(_event, number) => setPage(number + 1)}
          onRowsPerPageChange={(event) =>
            setPerPage(Number(event.target.value))
          }
        />
      </Card>
    </DashboardContent>
  );
}
