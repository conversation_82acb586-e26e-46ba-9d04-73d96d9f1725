import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import { useShowController } from 'react-admin';

import { Bundle } from '@portless/core';

import { EmptyContent } from '~/components/empty-content';
import { Iconify } from '~/components/iconify';
import { DashboardContent } from '~/layouts/dashboard';
import { RouterLink } from '~/routes/components';
import { paths } from '~/routes/paths';

import { BundleDetailsToolbar } from '../bundle-details-toolbar';
import { BundleInventoryTable } from '../bundle-inventory-table';
import { BundleDetailsSkeleton } from '../bundle-skeleton';

export function BundleDetailsView() {
  const { error, record, isPending } = useShowController<
    Bundle & { id: string }
  >();

  if (isPending) {
    return (
      <DashboardContent sx={{ pt: 5 }}>
        <BundleDetailsSkeleton />
      </DashboardContent>
    );
  }

  if (error) {
    return (
      <DashboardContent sx={{ pt: 5 }}>
        <EmptyContent
          filled
          title="Product not found!"
          action={
            <Button
              component={RouterLink}
              href={paths.product.root}
              startIcon={<Iconify width={16} icon="eva:arrow-ios-back-fill" />}
              sx={{ mt: 3 }}
            >
              Back to list
            </Button>
          }
          sx={{ py: 10, height: 'auto', flexGrow: 'unset' }}
        />
      </DashboardContent>
    );
  }

  // merge inventory
  const inventory = record.inventory?.length
    ? record.inventory
    : [
        {
          location_id: record.id,
          available: 0,
          committed: 0,
          incoming: 0,
          on_hand: 0,
          reserved: 0,
        },
      ];

  const total_inventory = inventory.reduce((total, current) => ({
    location_id: total.location_id,
    available: total.available + current.available,
    committed: total.committed + current.committed,
    incoming: total.incoming + current.incoming,
    on_hand: total.on_hand + current.on_hand,
    reserved: total.reserved + current.reserved,
  }));

  return (
    <DashboardContent>
      <BundleDetailsToolbar
        title={record.name}
        backLink={paths.product.root}
        available={total_inventory.available}
        sku={record.sku}
      />

      <Card>
        <CardHeader title="Locations" />
        <CardContent>
          <BundleInventoryTable inventory={inventory} />
        </CardContent>
      </Card>
    </DashboardContent>
  );
}
