import IconButton from '@mui/material/IconButton';
import type { StackProps } from '@mui/material/Stack';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';

import { Iconify } from '~/components/iconify';
import { ProductStatusLabel } from '~/components/status-label';
import { RouterLink } from '~/routes/components';
import { Label } from '~/components/label';

// ----------------------------------------------------------------------

type Props = StackProps & {
  available: number;
  backLink: string;
  sku: string;
};

export function BundleDetailsToolbar({
  title,
  available,
  backLink,
  sku,
  sx,
  ...other
}: Props) {
  return (
    <Stack
      spacing={3}
      direction={{ xs: 'column', md: 'row' }}
      sx={{
        mb: { xs: 3, md: 5 },
      }}
    >
      <Stack spacing={1} direction="row" alignItems="flex-start">
        <IconButton component={RouterLink} href={backLink}>
          <Iconify icon="eva:arrow-ios-back-fill" />
        </IconButton>

        <Stack spacing={0.5}>
          <Stack spacing={1} direction="row" alignItems="center">
            <Typography variant="h4"> {title} </Typography>
            <ProductStatusLabel quantity={available} />
          </Stack>

          <Typography variant="body2" sx={{ color: 'text.disabled' }}>
            <Label variant="soft">SKU</Label> {sku}
          </Typography>
        </Stack>
      </Stack>

      <Stack
        flexGrow={1}
        spacing={1.5}
        direction="row"
        alignItems="center"
        justifyContent="flex-end"
      >
        <Button
          color="inherit"
          variant="outlined"
          startIcon={<Iconify icon="solar:printer-minimalistic-bold" />}
        >
          Print
        </Button>
      </Stack>
    </Stack>
  );
}
