import Chip from '@mui/material/Chip';
import type { SxProps, Theme } from '@mui/material/styles';
import { isEmpty } from 'lodash';
import { useCallback } from 'react';

import {
  chipProps,
  FiltersBlock,
  FiltersResult,
} from '~/components/filters-result';
import type { IProductTableFilters } from '~/types/product';

// ----------------------------------------------------------------------

type Props = {
  totalResults: number;
  sx?: SxProps<Theme>;
  filters: IProductTableFilters;
  setFilters: (filters: IProductTableFilters) => void;
};

export function BundleTableFiltersResult({
  filters,
  setFilters,
  totalResults,
  sx,
}: Props) {
  const handleRemoveSku = useCallback(
    () => setFilters({ ...filters, sku: '' }),
    [filters, setFilters]
  );

  const handleRemoveName = useCallback(
    () => setFilters({ ...filters, name: '' }),
    [filters, setFilters]
  );

  return (
    <FiltersResult
      totalResults={totalResults}
      onReset={() => setFilters({ ...filters, name: '', sku: '' })}
      sx={sx}
    >
      <FiltersBlock label="SKU:" isShow={!isEmpty(filters.sku)}>
        <Chip
          {...chipProps}
          key={`filters-sku}`}
          label={filters.sku}
          onDelete={() => handleRemoveSku()}
        />
      </FiltersBlock>

      <FiltersBlock label="Name:" isShow={!isEmpty(filters.name)}>
        <Chip
          {...chipProps}
          key={`filters-name}`}
          label={filters.name}
          onDelete={() => handleRemoveName()}
        />
      </FiltersBlock>
    </FiltersResult>
  );
}
