# 🚀 PortlessApp - Postman Setup

## 📦 Arquivos Gerados

1. **PortlessApp-Postman-Collection.json** - Collection com todas as APIs
2. **PortlessApp-Environment.json** - Environment com variáveis locais
3. **POSTMAN-SETUP.md** - Este arquivo com instruções

## 🔧 Como Importar no Postman

### 1. Importar Collection
1. Abra o Postman
2. Clique em **"Import"**
3. Selecione o arquivo `PortlessApp-Postman-Collection.json`
4. Clique **"Import"**

### 2. Importar Environment
1. No Postman, clique no ícone de **"Environments"** (⚙️)
2. Clique **"Import"**
3. Selecione o arquivo `PortlessApp-Environment.json`
4. Clique **"Import"**
5. **Ative o environment** "PortlessApp - Local Development"

## 🧪 Como Testar

### APIs Públicas (sem autenticação)
✅ Teste primeiro estas APIs:
- `🌐 Public APIs > Public API - Root`
- `🌐 Public APIs > Public API - Health Check`

### APIs Protegidas (com autenticação)
Para testar APIs que precisam de auth:

1. **Faça login no Seller Portal:**
   - Acesse: `http://localhost:3000`
   - Login: `<EMAIL>`
   - Senha: `123456`

2. **Pegue o Token do Firebase:**
   - Abra DevTools (F12)
   - Console > Digite:
   ```javascript
   firebase.auth().currentUser.getIdToken().then(token => console.log(token))
   ```
   - Copie o token

3. **Configure no Postman:**
   - No Environment, cole o token na variável `authToken`
   - Ou use diretamente no header: `Authorization: Bearer <token>`

## 🎯 URLs Principais

| Serviço | URL |
|---------|-----|
| **Emulator UI** | http://127.0.0.1:4000 |
| **Seller Portal** | http://localhost:3000 |
| **Admin Portal** | http://127.0.0.1:5100 |
| **Functions Base** | http://127.0.0.1:5001/demo-portless/us-central1 |

## 📋 APIs Disponíveis

### 🌐 APIs Públicas
- `portless-public-httpsApi` - API pública principal

### 🛒 Seller Portal
- `portless-seller-httpsApi` - API do portal do vendedor

### 🔧 Admin
- `portless-admin-httpsApi` - API administrativa

### 📦 OMS (Order Management)
- `oms-api` - Sistema de gestão de pedidos

### 🏢 Organization
- `services-organization-api` - Gestão de organizações

### 📦 Mabang (Integração)
- `mabang-orders-sync-process` - Sincronização de pedidos
- `mabang-products-sync-process` - Sincronização de produtos
- `mabang-http-orders-backfill` - Backfill de pedidos

### 📍 Tracking
- `track17-updateTracking` - Atualização de rastreamento
- `track17-http-onTrackingUpdateSaveToGcs` - Salvar tracking no GCS

### 🌍 Zonos
- `services-zonos-api` - API de serviços Zonos

## 🔐 Autenticação

A maioria das APIs usa **Firebase Auth Bearer Token**.

**Headers necessários:**
```
Authorization: Bearer <firebase-token>
Content-Type: application/json
```

## 🚨 Troubleshooting

### Erro 404
- Verifique se os emulators estão rodando: `npm run dev`
- Confirme a URL base: `http://127.0.0.1:5001/demo-portless/us-central1`

### Erro 401/403
- Verifique se o token Firebase está válido
- Faça login novamente no seller-portal
- Gere um novo token

### Erro de CORS
- APIs locais não devem ter problema de CORS
- Se houver, teste direto no browser primeiro

## 🎯 Próximos Passos

1. **Teste APIs públicas primeiro**
2. **Configure autenticação**
3. **Explore endpoints específicos**
4. **Documente responses interessantes**

---

**🚀 Happy Testing!**
